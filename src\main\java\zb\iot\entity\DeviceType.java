package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("device_type")
public class DeviceType {
    
    private static final long serialVersionUID = 1L;

    /**
     * 产品ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 所属展览馆ID
     */
    private Integer exhibitionId;

    /**
     * 类型名称
     */
    private String name;

    /**
     * 类型名称
     */
    private String description;

    /**
     * 状态(1:发布,0:未发布)
     */
    private Byte status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Byte deleted;
}
