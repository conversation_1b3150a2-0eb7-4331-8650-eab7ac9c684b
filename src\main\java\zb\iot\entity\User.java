package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户信息表
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
@TableName("user")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户昵称
     */
    @TableField("username")
    private String username;

    /**
     * 头像路径
     */
    @TableField("avatar_path")
    private String avatarPath;

    /**
     * 密码(加密存储)
     */
    @TableField("password")
    private String password;

    /**
     * 手机号码
     */
    @TableField("phone")
    private String phone;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 注册时间
     */
    @TableField(value = "register_time", fill = FieldFill.INSERT)
    private LocalDateTime registerTime;

    /**
     * 最后登录时间
     */
    @TableField(value = "last_login_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastLoginTime;

    /**
     * 注册时使用的邀请码
     */
    @TableField("invitation_code")
    private String invitationCode;

    /**
     * 账号状态(1:正常,0:禁用)
     */
    @TableField("status")
    private Byte status;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;
}
