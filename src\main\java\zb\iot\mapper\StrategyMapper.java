package zb.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import zb.iot.controller.dto.StrategyQueryDTO;
import zb.iot.entity.Strategy;
import zb.iot.entity.VO.StrategyVO;

import java.util.List;

/**
 * 策略表 Mapper 接口
 * <AUTHOR>
 * @since 2025-01-31
 */
@Mapper
public interface StrategyMapper extends BaseMapper<Strategy> {

    /**
     * 分页查询策略列表
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 策略列表
     */
    IPage<StrategyVO> selectStrategyPage(Page<StrategyVO> page, @Param("query") StrategyQueryDTO queryDTO);

    /**
     * 根据ID查询策略详情
     * @param id 策略ID
     * @return 策略详情
     */
    StrategyVO selectStrategyById(@Param("id") Integer id);

    /**
     * 查询指定展厅的所有启用策略
     * @param exhibitionId 展厅ID
     * @return 策略列表
     */
    List<Strategy> selectEnabledStrategiesByExhibitionId(@Param("exhibitionId") Integer exhibitionId);

    /**
     * 查询指定类型的启用策略
     * @param exhibitionId 展厅ID
     * @param type 策略类型
     * @return 策略列表
     */
    List<Strategy> selectEnabledStrategiesByType(@Param("exhibitionId") Integer exhibitionId, @Param("type") Integer type);

    /**
     * 更新策略最后执行时间和执行次数
     * @param id 策略ID
     * @return 影响行数
     */
    int updateLastExecuted(@Param("id") Integer id);

    /**
     * 查询需要执行的定时策略
     * @return 策略列表
     */
    List<Strategy> selectScheduledStrategies();
}
