package zb.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import zb.iot.entity.Device;
import com.baomidou.mybatisplus.extension.service.IService;
import zb.iot.entity.VO.DeviceVO;
import zb.iot.entity.PolicyTag;

import java.util.List;

/**
 * <p>
 * 物联网设备表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface IDeviceService extends IService<Device> {

    /**
     * 添加设备
     * @param Device 设备信息
     * @return 添加的设备
     */
    Boolean addDevice(Device device);

    /**
     * 更新设备
     *
     * @param Device 设备信息
     * @return 更新后的设备
     */
    Boolean updateDevice(Device device);

    /**
     * 更新设备状态
     *
     * @param deviceId 设备ID
     * @param status 设备状态
     * @return 是否成功
     */
    Boolean updateDeviceStatus(Integer deviceId, Byte status);

    /**
     * 删除设备
     *
     * @param deviceId 设备ID
     * @return 是否成功
     */
    boolean deleteDevice(Integer deviceId);

    /**
     * 获取设备详情
     *
     * @param deviceId 设备ID
     * @return 设备详情
     */
    DeviceVO getDeviceById(Integer deviceId);

    /**
     * 分页查询设备
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 设备名称(可选)
     * @return 分页结果
     */
    IPage<Device> pageDevices(Integer pageNum, Integer pageSize, String name, Integer exhibitionId);

    /**
     * 获取设备的策略标签ID列表
     *
     * @param deviceId 设备ID
     * @return 策略标签ID列表
     */
    List<PolicyTag> getDevicePolicyTags(Integer deviceId);

    /**
     * 设置设备的策略标签
     *
     * @param deviceId 设备ID
     * @param policyTagIds 策略标签ID列表
     */
    void setDevicePolicyTags(Integer deviceId, List<PolicyTag> policyTagIds);

    /**
     * 网络唤醒设备
     *
     * @param deviceId 设备ID
     * @return 是否成功发送唤醒包
     */
    boolean wakeOnLan(Integer deviceId);

    List<Device> getDeviceList(Integer exhibitionId);

}
