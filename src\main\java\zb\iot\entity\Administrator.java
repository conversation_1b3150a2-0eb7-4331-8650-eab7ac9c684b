package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * 展厅管理员表
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
@TableName("administrator")
public class Administrator implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 管理员ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 展厅ID
     */
    @TableField("exhibition_id")
    private Integer exhibitionId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 管理员关联的权限列表 - 非数据库字段
     * 用于传输数据，不直接映射到数据库
     */
    @TableField(exist = false)
    private List<Integer> permissions;
}
