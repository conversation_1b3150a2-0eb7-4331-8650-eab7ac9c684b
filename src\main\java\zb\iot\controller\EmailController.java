package zb.iot.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import zb.iot.annotation.UserLog;
import zb.iot.common.ResponseResult;
import zb.iot.controller.dto.EmailCode;
import zb.iot.mapper.UserMapper;
import zb.iot.service.IEmailService;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Slf4j
public class EmailController {

    // 用于临时存储验证码，实际生产环境建议使用Redis等缓存
    private static final ConcurrentHashMap<String, EmailCode> emailCodeMap = new ConcurrentHashMap<>();

    private final IEmailService emailService;
    private final UserMapper userMapper;

    // 发送邮箱验证码
    @GetMapping("/sendCode")
    @UserLog(operationType = "EXECUTE", operationModule = "AUTH", description = "发送邮箱验证码")
    public ResponseResult<String> sendEmailCode(@RequestParam("email") String email) {

        try {
            // 生成随机验证码
            String code = UUID.randomUUID().toString().substring(0, 6).toUpperCase();

            // 构建邮件内容
            String subject = "【系统】邮箱验证码";
            String content = "您的验证码是：" + code + "，有效期5分钟。";

            // 发送邮件
            emailService.sendSimpleMail(email, subject, content);

            log.info("发送邮件成功: {}",code);
            // 存储验证码（包含有效期）
            EmailCode emailCode = new EmailCode(code, System.currentTimeMillis() + 5 * 60 * 1000);
            emailCodeMap.put(email, emailCode);

            return ResponseResult.success("验证码已发送至您的邮箱，请查收");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseResult.fail("发送验证码失败，请稍后重试");
        }
    }

    // 验证邮箱验证码
    @PostMapping("/verifyCode")
    @UserLog(operationType = "EXECUTE", operationModule = "AUTH", description = "验证邮箱验证码")
    public ResponseResult<String> verifyEmailCode(@RequestParam("email") String email,
                                               @RequestParam("code") String code) {

        log.info("验证邮箱验证码: {}",code);
        // 获取存储的验证码
        EmailCode storedCode = emailCodeMap.get(email);

        if (storedCode == null) {
            return ResponseResult.fail("验证码已过期，请重新获取");
        }

        // 检查验证码是否过期
        if (System.currentTimeMillis() > storedCode.getExpireTime()) {
            emailCodeMap.remove(email); // 移除过期验证码
            return ResponseResult.fail("验证码已过期，请重新获取");
        }

        // 验证邮箱（如果提供）
        if (email != null && !email.isEmpty()) {
            if (userMapper.countByEmail(email) > 0) {
                return ResponseResult.fail("邮箱已被注册");
            }
        }

        // 验证码不区分大小写
        if (storedCode.getCode().equalsIgnoreCase(code)) {
            return ResponseResult.success("验证成功");
        } else {
            return ResponseResult.fail("验证码错误，请重新输入");
        }
    }

    // /**
    //  * 使用邮箱和验证码注册
    //  */
    // @PostMapping("/registerWithEmail")
    // public ResponseResult<String> registerWithEmail(@RequestBody RegisterRequest request) {
    //     Map<String, Object> result = new HashMap<>();
    //
    //     String email = request.getEmail();
    //     String code = request.getCode();
    //
    //     // 先验证验证码
    //     ResponseResult<String> verifyResult = verifyEmailCode(email, code);
    //     if (!(Boolean) verifyResult.getCode().equals(HttpStatusEnum.SUCCESS.getCode())) {
    //         return verifyResult;
    //     }
    //
    //     try {
    //         authService.register(request);
    //
    //         // 注册成功后移除验证码
    //         emailCodeMap.remove(email);
    //
    //         return ResponseResult.success("注册成功");
    //     } catch (Exception e) {
    //         e.printStackTrace();
    //         return ResponseResult.fail("注册失败，请重试");
    //     }
    // }
}
