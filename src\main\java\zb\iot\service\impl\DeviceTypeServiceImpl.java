package zb.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import zb.iot.entity.DeviceType;
import zb.iot.mapper.DeviceTypeMapper;
import zb.iot.service.IDeviceTypeService;

import java.util.List;

/**
 * 设备类型表 服务实现类
 */
@Service
@RequiredArgsConstructor
public class DeviceTypeServiceImpl extends ServiceImpl<DeviceTypeMapper, DeviceType> implements IDeviceTypeService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addDeviceType(DeviceType deviceType) {
       return this.save(deviceType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDeviceType(DeviceType deviceType) {
        return this.updateById(deviceType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDeviceType(Integer id) {
        // 检查设备类型是否存在
        DeviceType existingDeviceType = this.getById(id);
        if (existingDeviceType == null) {
            return false;
        }

        // 删除设备类型
        return this.removeById(id);
    }

    @Override
    public DeviceType getDeviceTypeById(Integer id) {
        return this.getById(id);
    }

    @Override
    public IPage<DeviceType> pageDeviceTypes(Integer pageNum, Integer pageSize, String name, Integer exhibitionId) {
        Page<DeviceType> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<DeviceType> queryWrapper = new LambdaQueryWrapper<>();
        
        // 如果提供了展厅ID，进行筛选
        if (exhibitionId != null) {
            queryWrapper.eq(DeviceType::getExhibitionId, exhibitionId);
        }
        
        // 如果提供了名称，进行模糊查询
        if (StringUtils.hasText(name)) {
            queryWrapper.like(DeviceType::getName, name);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(DeviceType::getCreateTime);
        
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishDeviceType(Integer id) {
        DeviceType deviceType = this.getById(id);
        if (deviceType == null) {
            throw new RuntimeException("设备类型不存在");
        }
        
        // 设置为已发布状态
        deviceType.setStatus((byte) 1);
        
        return this.updateById(deviceType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unpublishDeviceType(Integer id) {
        DeviceType deviceType = this.getById(id);
        if (deviceType == null) {
            throw new RuntimeException("设备类型不存在");
        }
        
        // 设置为未发布状态
        deviceType.setStatus((byte) 0);
        
        return this.updateById(deviceType);
    }
    
    @Override
    public List<DeviceType> getPublishedDeviceTypes(Integer exhibitionId) {
        LambdaQueryWrapper<DeviceType> queryWrapper = new LambdaQueryWrapper<>();
        
        // 筛选已发布状态的设备类型
        queryWrapper.eq(DeviceType::getStatus, (byte) 1);
        
        // 如果提供了展厅ID，进行筛选
        if (exhibitionId != null) {
            queryWrapper.eq(DeviceType::getExhibitionId, exhibitionId);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(DeviceType::getCreateTime);
        
        return this.list(queryWrapper);
    }
}
