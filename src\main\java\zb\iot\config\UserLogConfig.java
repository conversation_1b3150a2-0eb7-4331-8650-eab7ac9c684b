package zb.iot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 用户日志配置类
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.user-log")
public class UserLogConfig {

    /**
     * 日志保留天数
     */
    private Integer retentionDays = 90;

    /**
     * 定时清理任务cron表达式
     */
    private String cleanupCron = "0 0 2 * * ?";

    /**
     * 是否启用定时清理
     */
    private Boolean cleanupEnabled = true;

    /**
     * 批量删除大小
     */
    private Integer batchSize = 1000;
}
