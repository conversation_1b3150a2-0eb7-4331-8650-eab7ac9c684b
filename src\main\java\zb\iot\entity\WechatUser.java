package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 微信用户关联表
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
@TableName("wechat_user")
public class WechatUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关联ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 微信OpenID
     */
    @TableField("open_id")
    private String openId;

    /**
     * 微信UnionID
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 微信昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 微信头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;
} 