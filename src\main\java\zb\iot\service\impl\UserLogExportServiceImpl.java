package zb.iot.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import zb.iot.controller.dto.UserLogQueryDTO;
import zb.iot.entity.UserOperationLog;
import zb.iot.entity.VO.UserOperationLogVO;
import zb.iot.mapper.UserOperationLogMapper;
import zb.iot.service.IUserLogExportService;
import zb.iot.service.IUserOperationLogService;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 用户日志导出服务实现类
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserLogExportServiceImpl implements IUserLogExportService {

    private final IUserOperationLogService userOperationLogService;
    private final UserOperationLogMapper userOperationLogMapper;

    @Override
    public void exportToExcel(UserLogQueryDTO queryDTO, HttpServletResponse response) {
        try {
            log.info("开始导出用户日志，查询条件: {}", queryDTO);

            // 设置不分页，获取所有数据
            queryDTO.setPageNum(1);
            queryDTO.setPageSize(Integer.MAX_VALUE);

            // 获取数据
            IPage<UserOperationLogVO> page = userOperationLogService.getUserLogPage(queryDTO);
            List<UserOperationLogVO> logs = page.getRecords();

            log.info("查询到日志数据: {} 条", logs.size());

            if (logs.isEmpty()) {
                log.warn("没有查询到日志数据");
            }

            // 设置响应头（在获取输出流之前）
            String fileName = "用户操作日志_" + DateUtil.format(new Date(), "yyyyMMdd_HHmmss") + ".xlsx";
            setExcelResponse(response, fileName);
            log.info("响应头设置完成，文件名: {}", fileName);

            // 创建Excel
            ExcelWriter writer = ExcelUtil.getWriter();
            log.info("Excel Writer 创建成功");

            // 设置表头
            setExcelHeaders(writer);
            log.info("Excel 表头设置完成");

            // 写入数据
            writeExcelData(writer, logs);
            log.info("Excel 数据写入完成");

            // 输出到响应流
            ServletOutputStream out = response.getOutputStream();
            log.info("获取输出流成功");

            // 直接将数据写入输出流（不要先调用无参的flush()）
            writer.flush(out, true);
            log.info("Excel 数据刷新到输出流完成");

            // 确保输出流刷新
            out.flush();
            log.info("输出流刷新完成");

            writer.close();
            log.info("Writer关闭完成");

            log.info("导出流程完成");

            log.info("用户日志导出成功，导出记录数: {}", logs.size());

        } catch (Exception e) {
            log.error("导出用户日志失败", e);
            try {
                // 重置响应
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"success\":false,\"message\":\"导出失败: " + e.getMessage() + "\"}");
                response.getWriter().flush();
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    @Override
    public void batchExportToExcel(List<Long> logIds, HttpServletResponse response) {
        try {
            if (logIds == null || logIds.isEmpty()) {
                throw new IllegalArgumentException("日志ID列表不能为空");
            }

            // 查询指定ID的日志
            QueryWrapper<UserOperationLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", logIds);
            queryWrapper.orderByDesc("create_time");

            List<UserOperationLog> logs = userOperationLogService.list(queryWrapper);

            // 转换为VO
            List<UserOperationLogVO> logVOs = convertToVOs(logs);

            // 创建Excel
            ExcelWriter writer = ExcelUtil.getWriter();

            // 设置表头
            setExcelHeaders(writer);

            // 写入数据
            writeExcelData(writer, logVOs);

            // 设置响应头
            String fileName = "用户操作日志_批量导出_" + DateUtil.format(new Date(), "yyyyMMdd_HHmmss") + ".xlsx";
            setExcelResponse(response, fileName);

            // 输出到响应流
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);

            log.info("批量导出用户日志成功，导出记录数: {}", logs.size());

        } catch (Exception e) {
            log.error("批量导出用户日志失败", e);
            try {
                // 重置响应
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"success\":false,\"message\":\"批量导出失败: " + e.getMessage() + "\"}");
                response.getWriter().flush();
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    @Override
    public void exportStatisticsReport(Integer exhibitionId, String startTime, String endTime, HttpServletResponse response) {
        try {
            // 解析时间
            LocalDateTime start = StrUtil.isNotBlank(startTime) ? 
                LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : 
                LocalDateTime.now().minusDays(30);
            LocalDateTime end = StrUtil.isNotBlank(endTime) ? 
                LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : 
                LocalDateTime.now();

            // 创建Excel工作簿
            ExcelWriter writer = ExcelUtil.getWriter();

            // 写入统计报告
            writeStatisticsReport(writer, exhibitionId, start, end);

            // 设置响应头
            String fileName = "用户日志统计报告_" + DateUtil.format(new Date(), "yyyyMMdd_HHmmss") + ".xlsx";
            setExcelResponse(response, fileName);

            // 输出到响应流
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);

            log.info("导出用户日志统计报告成功");

        } catch (Exception e) {
            log.error("导出用户日志统计报告失败", e);
            try {
                // 重置响应
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"success\":false,\"message\":\"导出统计报告失败: " + e.getMessage() + "\"}");
                response.getWriter().flush();
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    @Override
    public String generateExcelFile(UserLogQueryDTO queryDTO) {
        // 这里可以实现异步生成Excel文件，并返回下载链接
        // 暂时返回空，可以后续扩展
        return null;
    }

    /**
     * 设置Excel表头
     */
    private void setExcelHeaders(ExcelWriter writer) {
        // 设置表头别名
        writer.addHeaderAlias("id", "日志ID");
        writer.addHeaderAlias("username", "用户名");
        writer.addHeaderAlias("operationTypeName", "操作类型");
        writer.addHeaderAlias("operationModuleName", "操作模块");
        writer.addHeaderAlias("operationDetail", "操作详情");
        writer.addHeaderAlias("targetName", "操作目标");
        writer.addHeaderAlias("ipAddress", "IP地址");
        writer.addHeaderAlias("requestMethod", "请求方法");
        writer.addHeaderAlias("requestUrl", "请求URL");
        writer.addHeaderAlias("responseStatusName", "响应状态");
        writer.addHeaderAlias("errorMessage", "错误信息");
        writer.addHeaderAlias("executionTime", "执行耗时(ms)");
        writer.addHeaderAlias("createTimeFormatted", "操作时间");

        // 设置列宽
        writer.setColumnWidth(0, 10);  // 日志ID
        writer.setColumnWidth(1, 15);  // 用户名
        writer.setColumnWidth(2, 12);  // 操作类型
        writer.setColumnWidth(3, 12);  // 操作模块
        writer.setColumnWidth(4, 30);  // 操作详情
        writer.setColumnWidth(5, 20);  // 操作目标
        writer.setColumnWidth(6, 15);  // IP地址
        writer.setColumnWidth(7, 10);  // 请求方法
        writer.setColumnWidth(8, 40);  // 请求URL
        writer.setColumnWidth(9, 12);  // 响应状态
        writer.setColumnWidth(10, 30); // 错误信息
        writer.setColumnWidth(11, 12); // 执行耗时
        writer.setColumnWidth(12, 20); // 操作时间
    }

    /**
     * 写入Excel数据
     */
    private void writeExcelData(ExcelWriter writer, List<UserOperationLogVO> logs) {
        log.info("开始写入Excel数据，数据条数: {}", logs.size());

        // 准备数据
        List<Map<String, Object>> rows = new ArrayList<>();
        for (UserOperationLogVO log : logs) {
            Map<String, Object> row = new LinkedHashMap<>();
            row.put("id", log.getId());
            row.put("username", log.getUsername());
            row.put("operationTypeName", log.getOperationTypeName());
            row.put("operationModuleName", log.getOperationModuleName());
            row.put("operationDetail", log.getOperationDetail());
            row.put("targetName", log.getTargetName());
            row.put("ipAddress", log.getIpAddress());
            row.put("requestMethod", log.getRequestMethod());
            row.put("requestUrl", log.getRequestUrl());
            row.put("responseStatusName", log.getResponseStatusName());
            row.put("errorMessage", log.getErrorMessage());
            row.put("executionTime", log.getExecutionTime());
            row.put("createTimeFormatted", log.getCreateTimeFormatted());
            rows.add(row);
        }

        log.info("准备写入的行数: {}", rows.size());

        // 写入数据
        writer.write(rows, true);

        log.info("Excel数据写入完成");
    }

    /**
     * 写入统计报告
     */
    private void writeStatisticsReport(ExcelWriter writer, Integer exhibitionId, LocalDateTime startTime, LocalDateTime endTime) {
        // 获取统计数据
        List<Map<String, Object>> operationTypeStats = userOperationLogMapper.selectOperationTypeStatistics(exhibitionId, startTime, endTime);
        List<Map<String, Object>> operationModuleStats = userOperationLogMapper.selectOperationModuleStatistics(exhibitionId, startTime, endTime);
        List<Map<String, Object>> dailyStats = userOperationLogMapper.selectDailyOperationStatistics(exhibitionId, startTime, endTime);

        // 写入报告标题
        writer.merge(0, 0, 0, 4, "用户操作日志统计报告", true);
        writer.writeCellValue(0, 1, "统计时间: " + startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + 
                                   " 至 " + endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        int currentRow = 3;

        // 操作类型统计
        writer.writeCellValue(0, currentRow++, "操作类型统计");
        writer.writeCellValue(0, currentRow, "操作类型");
        writer.writeCellValue(1, currentRow++, "操作次数");
        
        for (Map<String, Object> stat : operationTypeStats) {
            writer.writeCellValue(0, currentRow, getOperationTypeName(stat.get("name").toString()));
            writer.writeCellValue(1, currentRow++, stat.get("value"));
        }

        currentRow += 2;

        // 操作模块统计
        writer.writeCellValue(0, currentRow++, "操作模块统计");
        writer.writeCellValue(0, currentRow, "操作模块");
        writer.writeCellValue(1, currentRow++, "操作次数");
        
        for (Map<String, Object> stat : operationModuleStats) {
            writer.writeCellValue(0, currentRow, getOperationModuleName(stat.get("name").toString()));
            writer.writeCellValue(1, currentRow++, stat.get("value"));
        }
    }

    /**
     * 设置Excel响应头
     */
    private void setExcelResponse(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
    }

    /**
     * 转换为VO对象
     */
    private List<UserOperationLogVO> convertToVOs(List<UserOperationLog> logs) {
        List<UserOperationLogVO> vos = new ArrayList<>();
        for (UserOperationLog log : logs) {
            UserOperationLogVO vo = new UserOperationLogVO();
            vo.setId(log.getId());
            vo.setUserId(log.getUserId());
            vo.setUsername(log.getUsername());
            vo.setOperationType(log.getOperationType());
            vo.setOperationTypeName(getOperationTypeName(log.getOperationType()));
            vo.setOperationModule(log.getOperationModule());
            vo.setOperationModuleName(getOperationModuleName(log.getOperationModule()));
            vo.setOperationDetail(log.getOperationDetail());
            vo.setTargetId(log.getTargetId());
            vo.setTargetName(log.getTargetName());
            vo.setIpAddress(log.getIpAddress());
            vo.setUserAgent(log.getUserAgent());
            vo.setRequestUrl(log.getRequestUrl());
            vo.setRequestMethod(log.getRequestMethod());
            vo.setRequestParams(log.getRequestParams());
            vo.setResponseStatus(log.getResponseStatus());
            vo.setResponseStatusName(getResponseStatusName(log.getResponseStatus()));
            vo.setErrorMessage(log.getErrorMessage());
            vo.setExecutionTime(log.getExecutionTime());
            vo.setExhibitionId(log.getExhibitionId());
            vo.setCreateTime(log.getCreateTime());
            vo.setCreateTimeFormatted(log.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            vos.add(vo);
        }
        return vos;
    }

    private String getOperationTypeName(String type) {
        Map<String, String> typeMap = Map.of(
            "LOGIN", "登录",
            "LOGOUT", "登出",
            "CREATE", "创建",
            "UPDATE", "更新",
            "DELETE", "删除",
            "EXECUTE", "执行",
            "VIEW", "查看",
            "EXPORT", "导出",
            "IMPORT", "导入"
        );
        return typeMap.getOrDefault(type, type);
    }

    private String getOperationModuleName(String module) {
        Map<String, String> moduleMap = Map.of(
            "STRATEGY", "策略管理",
            "DEVICE", "设备管理",
            "USER", "用户管理",
            "EXHIBITION", "展厅管理",
            "SYSTEM", "系统管理",
            "AUTH", "认证授权"
        );
        return moduleMap.getOrDefault(module, module);
    }

    private String getResponseStatusName(String status) {
        Map<String, String> statusMap = Map.of(
            "SUCCESS", "成功",
            "FAILED", "失败",
            "ERROR", "错误"
        );
        return statusMap.getOrDefault(status, status);
    }
}
