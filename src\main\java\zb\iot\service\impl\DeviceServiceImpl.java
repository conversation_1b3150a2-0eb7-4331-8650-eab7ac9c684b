package zb.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import zb.iot.entity.*;
import zb.iot.entity.VO.DeviceVO;
import zb.iot.mapper.DeviceMapper;
import zb.iot.mapper.DevicePolicyTagMapper;
import zb.iot.mapper.DeviceTypeMapper;
import zb.iot.mapper.ProtocolTypeMapper;
import zb.iot.service.IDeviceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import zb.iot.utils.WakeOnLanUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 物联网设备表 服务实现类
 * <AUTHOR>
 * @since 2025-03-21
 */
@Service
@RequiredArgsConstructor
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements IDeviceService {

    private final DevicePolicyTagMapper devicePolicyTagMapper;
    private final DeviceTypeMapper deviceTypeMapper;
    private final ProtocolTypeMapper protocolTypeMapper;


    @Override
    public List<Device> getDeviceList(Integer exhibitionId) {

        return this.list(new LambdaQueryWrapper<Device>()
                .eq(Device::getExhibitionId, exhibitionId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addDevice(Device device) {
        // 1. 创建设备对象
        device.setStatus((byte)0); // 默认离线

        boolean save = this.save(device);

        // 2. 如果有策略标签，设置关联
        if (device.getPolicyTag() != null && !device.getPolicyTag().isEmpty()) {
            setDevicePolicyTags(device.getId(), device.getPolicyTag());
        }

        return save;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDevice(Device device) {
        // 1. 检查设备是否存在
        Device oldDevice = this.getById(device.getId());
        if (oldDevice == null) {
            throw new RuntimeException("设备不存在");
        }

        // 2. 更新设备信息
        oldDevice.setName(device.getName());
        oldDevice.setIpAddress(device.getIpAddress());
        oldDevice.setStatus(device.getStatus());
        oldDevice.setIdentifier(device.getIdentifier());
        oldDevice.setProtocolTypeId(device.getProtocolTypeId());
        oldDevice.setDeviceTypeId(device.getDeviceTypeId());

        //3. 删除所有策略标签关联
        devicePolicyTagMapper.deleteByDeviceId(device.getId());

        //4. 更新策略标签关联
        if (device.getPolicyTag() != null && !device.getPolicyTag().isEmpty()) {
            setDevicePolicyTags(device.getId(), device.getPolicyTag());
        }

        //5. 返回更新后的设备信息
        return this.updateById(device);
    }

    @Override
    public Boolean updateDeviceStatus(Integer deviceId, Byte status) {
        Device oldDevice = this.getById(deviceId);
        if (oldDevice == null) {
            throw new RuntimeException("设备不存在");
        }
        oldDevice.setStatus(status);
        return this.updateById(oldDevice);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDevice(Integer deviceId) {
        // 1. 检查设备是否存在
        Device device = this.getById(deviceId);
        if (device == null) {
            return false;
        }

        // 2. 删除设备策略标签关联
        devicePolicyTagMapper.deleteByDeviceId(deviceId);

        // 3. 删除设备
        return this.removeById(deviceId);
    }

    @Override
    public DeviceVO getDeviceById(Integer deviceId) {
        // 1. 获取设备基本信息
        Device device = this.getById(deviceId);
        if (device == null) {
            return null;
        }

        //根据device的deviceType获取deviceType的详细信息
        DeviceType deviceTypeById = deviceTypeMapper.getDeviceTypeById(device.getDeviceTypeId());
        ProtocolType protocolType = protocolTypeMapper.getProtocolTypeById(device.getProtocolTypeId());

        //将Device的值赋给deviceVO
        DeviceVO deviceVO = new DeviceVO();
        BeanUtils.copyProperties(device, deviceVO);
        deviceVO.setDeviceType(deviceTypeById);
        deviceVO.setProtocolType(protocolType);

        // 2. 获取设备策略标签
        List<PolicyTag> policyTags = getDevicePolicyTags(deviceId);
        deviceVO.setPolicyTag(policyTags);
        return deviceVO;
    }

    @Override
    public IPage<Device> pageDevices(Integer pageNum, Integer pageSize, String name, Integer exhibitionId) {
        // 1. 分页查询设备
        Page<Device> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getExhibitionId, exhibitionId);
        if (StringUtils.hasText(name)){
            queryWrapper.like(Device::getName, name);
        }
        Page<Device> devicePage = this.page(page, queryWrapper);

        // 2. 查询每个设备的策略标签
        List<Device> deviceList = devicePage.getRecords();
        deviceList.forEach(device -> {
            List<PolicyTag> devicePolicyTags = getDevicePolicyTags(device.getId());
            device.setPolicyTag(devicePolicyTags);
        });

        // 3. 封装为新的分页对象
        IPage<Device> dtoPage = new Page<>(devicePage.getCurrent(), devicePage.getSize(), devicePage.getTotal());
        dtoPage.setRecords(deviceList);

        return dtoPage;
    }

    @Override
    public List<PolicyTag> getDevicePolicyTags(Integer deviceId) {
        return devicePolicyTagMapper.getPolicyTagIdsByDeviceId(deviceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDevicePolicyTags(Integer deviceId, List<PolicyTag> policyTags) {

        // 1. 创建新的关联
        List<DevicePolicyTag> relations = new ArrayList<>();

        for (PolicyTag tag : policyTags) {
            DevicePolicyTag relation = new DevicePolicyTag();
            relation.setDeviceId(deviceId);
            relation.setPolicyId(tag.getId());
            relations.add(relation);
        }

        // 4. 批量插入关联
        if (!relations.isEmpty()) {
            devicePolicyTagMapper.batchInsert(relations);
        }
    }
    
    @Override
    public boolean wakeOnLan(Integer deviceId) {
        // 1. 获取设备信息
        Device device = this.getById(deviceId);
        if (device == null) {
            return false;
        }
        
        // 2. 检查MAC地址是否存在
        String macAddress = device.getMacAddress();
        if (macAddress == null || macAddress.isEmpty()) {
            return false;
        }
        
        // 3. 发送网络唤醒包
        String ipAddress = device.getIpAddress();
        boolean result = WakeOnLanUtil.wakeUp(macAddress, ipAddress);
        
        // 4. 如果成功发送唤醒包，更新设备状态为在线
        if (result) {
            device.setStatus((byte) 1); // 设置为在线
            this.updateById(device);
        }
        
        return result;
    }


}
