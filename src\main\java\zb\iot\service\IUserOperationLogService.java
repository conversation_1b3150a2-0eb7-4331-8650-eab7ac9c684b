package zb.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import zb.iot.controller.dto.UserLogQueryDTO;
import zb.iot.entity.UserOperationLog;
import zb.iot.entity.VO.UserOperationLogVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户操作日志服务接口
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface IUserOperationLogService extends IService<UserOperationLog> {

    /**
     * 分页查询用户操作日志
     * @param queryDTO 查询条件
     * @return 日志分页数据
     */
    IPage<UserOperationLogVO> getUserLogPage(UserLogQueryDTO queryDTO);

    /**
     * 记录用户操作日志
     * @param userId 用户ID
     * @param username 用户名
     * @param operationType 操作类型
     * @param operationModule 操作模块
     * @param operationDetail 操作详情
     * @param targetId 目标ID
     * @param targetName 目标名称
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestParams 请求参数
     * @param responseStatus 响应状态
     * @param errorMessage 错误信息
     * @param executionTime 执行耗时
     * @param exhibitionId 展厅ID
     */
    void recordUserOperation(Integer userId, String username, String operationType, String operationModule,
                           String operationDetail, String targetId, String targetName, String ipAddress,
                           String userAgent, String requestUrl, String requestMethod, String requestParams,
                           String responseStatus, String errorMessage, Integer executionTime, Integer exhibitionId);

    /**
     * 记录用户登录日志
     * @param userId 用户ID
     * @param username 用户名
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param success 是否成功
     * @param errorMessage 错误信息
     */
    void recordLoginLog(Integer userId, String username, String ipAddress, String userAgent, boolean success, String errorMessage);

    /**
     * 记录用户登出日志
     * @param userId 用户ID
     * @param username 用户名
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void recordLogoutLog(Integer userId, String username, String ipAddress, String userAgent);

    /**
     * 查询用户最近的操作记录
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 操作记录列表
     */
    List<UserOperationLogVO> getRecentUserLogs(Integer userId, Integer limit);

    /**
     * 统计用户操作情况
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> getUserOperationStatistics(Integer userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计操作类型分布
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> getOperationTypeStatistics(Integer exhibitionId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计操作模块分布
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> getOperationModuleStatistics(Integer exhibitionId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计每日操作量
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> getDailyOperationStatistics(Integer exhibitionId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 清理过期的操作日志
     * @param beforeTime 时间点
     * @return 删除数量
     */
    int cleanExpiredLogs(LocalDateTime beforeTime);

    /**
     * 批量记录操作日志
     * @param logs 日志列表
     * @return 是否成功
     */
    boolean batchRecordLogs(List<UserOperationLog> logs);
}
