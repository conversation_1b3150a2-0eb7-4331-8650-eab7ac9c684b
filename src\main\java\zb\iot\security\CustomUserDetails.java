package zb.iot.security;

import lombok.Builder;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import zb.iot.entity.Permission;
import zb.iot.entity.User;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Builder
public class CustomUserDetails implements UserDetails {

    private final Integer userId;
    private final String username;
    private final String password;
    private final String phone;
    private final String email;
    private final Byte enabled;
    private final List<Integer> permissions;

    // 从User实体创建UserDetails
    public static CustomUserDetails fromUser(User user, List<Integer> permissions) {
        return CustomUserDetails.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .password(user.getPassword())
                .phone(user.getPhone())
                .email(user.getEmail())
                .enabled(user.getStatus())
                .permissions(permissions != null ? permissions : Collections.emptyList())
                .build();
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return permissions.stream()
                .map(IntegerPermission::new)
                .collect(Collectors.toList());
    }

    // 自定义GrantedAuthority，透明处理Integer→String转换
    private static class IntegerPermission implements GrantedAuthority {
        private final int permissionId;

        public IntegerPermission(int permissionId) {
            this.permissionId = permissionId;
        }

        @Override
        public String getAuthority() {
            return String.valueOf(permissionId); // 框架只关心字符串形式
        }

        // 可选：重写equals/hashCode方便权限比对
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            IntegerPermission that = (IntegerPermission) o;
            return permissionId == that.permissionId;
        }

        @Override
        public int hashCode() {
            return Integer.hashCode(permissionId);
        }
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return enabled == 1;
    }
} 