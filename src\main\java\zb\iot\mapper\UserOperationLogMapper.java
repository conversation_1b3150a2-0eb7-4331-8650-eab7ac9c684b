package zb.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import zb.iot.controller.dto.UserLogQueryDTO;
import zb.iot.entity.UserOperationLog;
import zb.iot.entity.VO.UserOperationLogVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户操作日志表 Mapper 接口
 * <AUTHOR>
 * @since 2025-01-31
 */
@Mapper
public interface UserOperationLogMapper extends BaseMapper<UserOperationLog> {

    /**
     * 分页查询用户操作日志
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 日志列表
     */
    IPage<UserOperationLogVO> selectUserLogPage(Page<UserOperationLogVO> page, @Param("query") UserLogQueryDTO queryDTO);

    /**
     * 查询用户最近的操作记录
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 操作记录列表
     */
    List<UserOperationLogVO> selectRecentUserLogs(@Param("userId") Integer userId, @Param("limit") Integer limit);

    /**
     * 统计用户操作情况
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> selectUserOperationStatistics(
            @Param("userId") Integer userId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 统计操作类型分布
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> selectOperationTypeStatistics(
            @Param("exhibitionId") Integer exhibitionId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 统计操作模块分布
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> selectOperationModuleStatistics(
            @Param("exhibitionId") Integer exhibitionId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 统计每日操作量
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> selectDailyOperationStatistics(
            @Param("exhibitionId") Integer exhibitionId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 清理过期的操作日志
     * @param beforeTime 时间点
     * @return 删除数量
     */
    int deleteExpiredLogs(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 批量清理过期的操作日志
     * @param beforeTime 时间点
     * @param batchSize 批量大小
     * @return 删除数量
     */
    int deleteExpiredLogsBatch(@Param("beforeTime") LocalDateTime beforeTime, @Param("batchSize") int batchSize);

    /**
     * 批量插入日志记录
     * @param logs 日志列表
     * @return 插入数量
     */
    int batchInsert(@Param("logs") List<UserOperationLog> logs);
}
