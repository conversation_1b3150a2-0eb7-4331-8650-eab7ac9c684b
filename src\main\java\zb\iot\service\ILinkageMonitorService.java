package zb.iot.service;

import zb.iot.entity.Strategy;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 联动监听服务接口
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface ILinkageMonitorService {

    /**
     * 启动联动策略监听
     * @param strategy 策略对象
     * @return 监听是否启动成功
     */
    boolean startLinkageMonitor(Strategy strategy);

    /**
     * 停止联动策略监听
     * @param strategyId 策略ID
     * @return 监听是否停止成功
     */
    boolean stopLinkageMonitor(Integer strategyId);

    /**
     * 更新联动策略监听
     * @param strategy 策略对象
     * @return 监听是否更新成功
     */
    boolean updateLinkageMonitor(Strategy strategy);

    /**
     * 获取所有活跃的联动监听
     * @return 活跃监听列表
     */
    List<LinkageMonitorInfo> getActiveMonitors();

    /**
     * 手动触发联动策略
     * @param strategyId 策略ID
     * @param triggerData 触发数据
     * @return 触发是否成功
     */
    boolean triggerLinkageStrategy(Integer strategyId, Object triggerData);

    /**
     * 检查策略是否有活跃的监听
     * @param strategyId 策略ID
     * @return 是否有活跃监听
     */
    boolean hasActiveMonitor(Integer strategyId);

    /**
     * 处理设备状态变化事件
     * @param deviceId 设备ID
     * @param statusType 状态类型
     * @param statusValue 状态值
     */
    void handleDeviceStatusChange(Integer deviceId, String statusType, Object statusValue);

    /**
     * 联动监听信息
     */
    class LinkageMonitorInfo {
        private Integer strategyId;
        private String strategyName;
        private Integer triggerDeviceId;
        private String triggerCondition;
        private Object triggerValue;
        private String status;
        private LocalDateTime startTime;
        private LocalDateTime lastTriggerTime;
        private Integer triggerCount;

        // 构造函数
        public LinkageMonitorInfo(Integer strategyId, String strategyName, Integer triggerDeviceId,
                                 String triggerCondition, Object triggerValue, String status,
                                 LocalDateTime startTime, LocalDateTime lastTriggerTime, Integer triggerCount) {
            this.strategyId = strategyId;
            this.strategyName = strategyName;
            this.triggerDeviceId = triggerDeviceId;
            this.triggerCondition = triggerCondition;
            this.triggerValue = triggerValue;
            this.status = status;
            this.startTime = startTime;
            this.lastTriggerTime = lastTriggerTime;
            this.triggerCount = triggerCount;
        }

        // Getters and Setters
        public Integer getStrategyId() { return strategyId; }
        public void setStrategyId(Integer strategyId) { this.strategyId = strategyId; }
        public String getStrategyName() { return strategyName; }
        public void setStrategyName(String strategyName) { this.strategyName = strategyName; }
        public Integer getTriggerDeviceId() { return triggerDeviceId; }
        public void setTriggerDeviceId(Integer triggerDeviceId) { this.triggerDeviceId = triggerDeviceId; }
        public String getTriggerCondition() { return triggerCondition; }
        public void setTriggerCondition(String triggerCondition) { this.triggerCondition = triggerCondition; }
        public Object getTriggerValue() { return triggerValue; }
        public void setTriggerValue(Object triggerValue) { this.triggerValue = triggerValue; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        public LocalDateTime getLastTriggerTime() { return lastTriggerTime; }
        public void setLastTriggerTime(LocalDateTime lastTriggerTime) { this.lastTriggerTime = lastTriggerTime; }
        public Integer getTriggerCount() { return triggerCount; }
        public void setTriggerCount(Integer triggerCount) { this.triggerCount = triggerCount; }
    }
}
