-- 策略管理相关数据库表

-- 策略表
DROP TABLE IF EXISTS `strategy`;
CREATE TABLE `strategy` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '策略ID',
  `exhibition_id` int NOT NULL COMMENT '所属展厅ID',
  `name` varchar(100) NOT NULL COMMENT '策略名称',
  `type` tinyint(1) NOT NULL COMMENT '策略类型(1:群组,2:定时,3:联动)',
  `description` text COMMENT '策略描述',
  `config` json NOT NULL COMMENT '策略配置(JSON格式)',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0:禁用,1:启用)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_executed` datetime DEFAULT NULL COMMENT '最后执行时间',
  `execution_count` int NOT NULL DEFAULT '0' COMMENT '执行次数',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_exhibition_id` (`exhibition_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略表';

-- 策略执行记录表
DROP TABLE IF EXISTS `strategy_execution_log`;
CREATE TABLE `strategy_execution_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `execution_time` datetime NOT NULL COMMENT '执行时间',
  `status` tinyint(1) NOT NULL COMMENT '执行状态(0:失败,1:成功,2:部分成功)',
  `result` text COMMENT '执行结果详情',
  `error_message` text COMMENT '错误信息',
  `execution_duration` int DEFAULT NULL COMMENT '执行耗时(毫秒)',
  `affected_devices` json DEFAULT NULL COMMENT '影响的设备列表(JSON格式)',
  `trigger_type` varchar(50) DEFAULT NULL COMMENT '触发类型(manual:手动,scheduled:定时,linkage:联动)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_execution_time` (`execution_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略执行记录表';

-- 策略设备关联表（用于群组策略）
DROP TABLE IF EXISTS `strategy_device`;
CREATE TABLE `strategy_device` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `device_id` int NOT NULL COMMENT '设备ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_device` (`strategy_id`,`device_id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略设备关联表';

-- 策略定时任务表（用于定时策略）
DROP TABLE IF EXISTS `strategy_schedule`;
CREATE TABLE `strategy_schedule` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `cron_expression` varchar(100) DEFAULT NULL COMMENT 'Cron表达式',
  `next_execution_time` datetime DEFAULT NULL COMMENT '下次执行时间',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_id` (`strategy_id`),
  KEY `idx_next_execution_time` (`next_execution_time`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略定时任务表';

-- 策略联动条件表（用于联动策略）
DROP TABLE IF EXISTS `strategy_linkage_condition`;
CREATE TABLE `strategy_linkage_condition` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '条件ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `trigger_device_id` int NOT NULL COMMENT '触发设备ID',
  `condition_type` varchar(50) NOT NULL COMMENT '条件类型',
  `condition_value` varchar(255) DEFAULT NULL COMMENT '条件值',
  `operator` varchar(20) DEFAULT NULL COMMENT '操作符(>,<,=,!=等)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_trigger_device_id` (`trigger_device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略联动条件表';

-- 策略联动动作表（用于联动策略）
DROP TABLE IF EXISTS `strategy_linkage_action`;
CREATE TABLE `strategy_linkage_action` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '动作ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `target_device_id` int NOT NULL COMMENT '目标设备ID',
  `action_type` varchar(50) NOT NULL COMMENT '动作类型',
  `action_value` varchar(255) DEFAULT NULL COMMENT '动作值',
  `delay_seconds` int NOT NULL DEFAULT '0' COMMENT '延迟执行秒数',
  `order_index` int NOT NULL DEFAULT '0' COMMENT '执行顺序',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_target_device_id` (`target_device_id`),
  KEY `idx_order_index` (`order_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略联动动作表';

-- 插入一些示例数据
INSERT INTO `strategy` (`exhibition_id`, `name`, `type`, `description`, `config`, `status`, `create_time`) VALUES
(3, '展厅灯光群组控制', 1, '统一控制展厅所有灯光设备的开关', 
 '{"groupName":"展厅灯光群组","devices":[{"id":1,"name":"主展区灯光","ipAddress":"*************"},{"id":2,"name":"入口灯光","ipAddress":"*************"}],"defaultAction":"power_on"}', 
 1, NOW()),
(3, '每日开馆定时策略', 2, '每日上午9点自动开启所有展示设备', 
 '{"scheduleType":"workday","executeTime":"09:00:00","targetDevices":[{"id":3,"name":"多媒体屏幕1","ipAddress":"*************"},{"id":4,"name":"多媒体屏幕2","ipAddress":"*************"}],"action":"power_on"}', 
 1, NOW()),
(3, '温度异常联动策略', 3, '当温度传感器检测到异常高温时，自动开启空调设备', 
 '{"triggerDeviceId":5,"triggerCondition":"temperature_above","triggerValue":"28","actions":[{"deviceId":6,"actionType":"power_on"}]}', 
 0, NOW());

-- 用户操作日志表
DROP TABLE IF EXISTS `user_operation_log`;
CREATE TABLE `user_operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `username` varchar(100) DEFAULT NULL COMMENT '用户名',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型(LOGIN,LOGOUT,CREATE,UPDATE,DELETE,EXECUTE,VIEW)',
  `operation_module` varchar(50) NOT NULL COMMENT '操作模块(STRATEGY,DEVICE,USER,EXHIBITION,SYSTEM)',
  `operation_detail` text COMMENT '操作详情描述',
  `target_id` varchar(100) DEFAULT NULL COMMENT '操作目标ID',
  `target_name` varchar(200) DEFAULT NULL COMMENT '操作目标名称',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理信息',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法(GET,POST,PUT,DELETE)',
  `request_params` json DEFAULT NULL COMMENT '请求参数(JSON格式)',
  `response_status` varchar(20) DEFAULT NULL COMMENT '响应状态(SUCCESS,FAILED,ERROR)',
  `error_message` text COMMENT '错误信息',
  `execution_time` int DEFAULT NULL COMMENT '执行耗时(毫秒)',
  `exhibition_id` int DEFAULT NULL COMMENT '所属展厅ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_module` (`operation_module`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_exhibition_id` (`exhibition_id`),
  KEY `idx_ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户操作日志表';

-- 添加权限数据
INSERT INTO `permission` (`name`, `description`, `code`) VALUES
('策略查看', '查看策略信息权限', 'STRATEGY_VIEW'),
('策略管理', '管理策略信息权限', 'STRATEGY_MANAGE'),
('用户日志查看', '查看用户操作日志权限', 'USER_LOG_VIEW'),
('用户日志管理', '管理用户操作日志权限', 'USER_LOG_MANAGE');
