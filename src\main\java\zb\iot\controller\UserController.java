package zb.iot.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import zb.iot.common.ResponseResult;
import zb.iot.controller.dto.AuthResponse;
import zb.iot.controller.dto.RegisterRequest;
import zb.iot.entity.User;
import zb.iot.security.CustomUserDetails;
import zb.iot.service.IAuthService;

/**
 * <p>
 * 用户信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

}
