package zb.iot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import zb.iot.annotation.UserLog;
import zb.iot.common.ResponseResult;
import zb.iot.controller.dto.StrategyDTO;
import zb.iot.controller.dto.StrategyQueryDTO;
import zb.iot.entity.Strategy;
import zb.iot.entity.StrategyExecutionLog;
import zb.iot.entity.VO.StrategyVO;
import zb.iot.service.IStrategyService;

import java.util.List;

/**
 * 策略管理控制器
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@RestController
@RequestMapping("/strategies")
@RequiredArgsConstructor
@Validated
public class StrategyController {

    private final IStrategyService strategyService;

    /**
     * 分页查询策略列表
     * @param queryDTO 查询条件
     * @return 策略分页数据
     */
    @GetMapping
    public ResponseResult<IPage<StrategyVO>> getStrategyPage(StrategyQueryDTO queryDTO) {
        try {
            IPage<StrategyVO> page = strategyService.getStrategyPage(queryDTO);
            return ResponseResult.success(page);
        } catch (Exception e) {
            log.error("查询策略列表失败", e);
            return ResponseResult.fail("查询策略列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询策略详情
     * @param id 策略ID
     * @return 策略详情
     */
    @GetMapping("/{id}")
    public ResponseResult<StrategyVO> getStrategyById(@PathVariable Integer id) {
        try {
            StrategyVO strategy = strategyService.getStrategyById(id);
            if (strategy == null) {
                return ResponseResult.fail("策略不存在");
            }
            return ResponseResult.success(strategy);
        } catch (Exception e) {
            log.error("查询策略详情失败，策略ID: {}", id, e);
            return ResponseResult.fail("查询策略详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建策略
     * @param strategyDTO 策略数据
     * @return 创建结果
     */
    @PostMapping
    @UserLog(operationType = "CREATE", operationModule = "STRATEGY", description = "创建策略")
    public ResponseResult<Strategy> createStrategy(@Valid @RequestBody StrategyDTO strategyDTO) {
        try {
            Strategy strategy = strategyService.createStrategy(strategyDTO);
            return ResponseResult.success(strategy);
        } catch (Exception e) {
            log.error("创建策略失败", e);
            return ResponseResult.fail("创建策略失败: " + e.getMessage());
        }
    }

    /**
     * 更新策略
     * @param id 策略ID
     * @param strategyDTO 策略数据
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @UserLog(operationType = "UPDATE", operationModule = "STRATEGY", description = "更新策略")
    public ResponseResult<Strategy> updateStrategy(@PathVariable Integer id, @Valid @RequestBody StrategyDTO strategyDTO) {
        try {
            strategyDTO.setId(id);
            Strategy strategy = strategyService.updateStrategy(strategyDTO);
            return ResponseResult.success(strategy);
        } catch (Exception e) {
            log.error("更新策略失败，策略ID: {}", id, e);
            return ResponseResult.fail("更新策略失败: " + e.getMessage());
        }
    }

    /**
     * 删除策略
     * @param id 策略ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @UserLog(operationType = "DELETE", operationModule = "STRATEGY", description = "删除策略")
    public ResponseResult<Void> deleteStrategy(@PathVariable Integer id) {
        try {
            boolean success = strategyService.deleteStrategy(id);
            if (success) {
                return ResponseResult.success();
            } else {
                return ResponseResult.fail("删除策略失败");
            }
        } catch (Exception e) {
            log.error("删除策略失败，策略ID: {}", id, e);
            return ResponseResult.fail("删除策略失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用策略
     * @param id 策略ID
     * @param status 状态(0:禁用,1:启用)
     * @return 操作结果
     */
    @PutMapping("/{id}/status")
    @UserLog(operationType = "UPDATE", operationModule = "STRATEGY", description = "启用/禁用策略")
    public ResponseResult<String> toggleStrategyStatus(@PathVariable Integer id, @RequestParam Integer status) {
        try {
            boolean success = strategyService.toggleStrategyStatus(id, status);
            if (success) {
                String statusText = status == Strategy.STATUS_ENABLED ? "启用" : "禁用";
                return ResponseResult.success("策略" + statusText + "成功");
            } else {
                return ResponseResult.fail("更新策略状态失败");
            }
        } catch (Exception e) {
            log.error("更新策略状态失败，策略ID: {}, 状态: {}", id, status, e);
            return ResponseResult.fail("更新策略状态失败: " + e.getMessage());
        }
    }

    /**
     * 执行策略
     * @param id 策略ID
     * @return 执行结果
     */
    @PostMapping("/{id}/execute")
    @UserLog(operationType = "EXECUTE", operationModule = "STRATEGY", description = "执行策略")
    public ResponseResult<StrategyExecutionLog> executeStrategy(@PathVariable Integer id) {
        try {
            StrategyExecutionLog executionLog = strategyService.executeStrategy(id);
            return ResponseResult.success(executionLog);
        } catch (Exception e) {
            log.error("执行策略失败，策略ID: {}", id, e);
            return ResponseResult.fail("执行策略失败: " + e.getMessage());
        }
    }

    /**
     * 批量执行策略
     * @param strategyIds 策略ID列表
     * @return 执行结果列表
     */
    @PostMapping("/batch-execute")
    @UserLog(operationType = "EXECUTE", operationModule = "STRATEGY", description = "批量执行策略")
    public ResponseResult<List<StrategyExecutionLog>> batchExecuteStrategies(@RequestBody List<Integer> strategyIds) {
        try {
            List<StrategyExecutionLog> results = strategyService.batchExecuteStrategies(strategyIds);
            return ResponseResult.success(results);
        } catch (Exception e) {
            log.error("批量执行策略失败", e);
            return ResponseResult.fail("批量执行策略失败: " + e.getMessage());
        }
    }

    /**
     * 查询策略执行历史
     * @param id 策略ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 执行历史分页数据
     */
    @GetMapping("/{id}/execution-history")
    public ResponseResult<IPage<StrategyExecutionLog>> getExecutionHistory(
            @PathVariable Integer id,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            IPage<StrategyExecutionLog> page = strategyService.getExecutionHistory(id, pageNum, pageSize);
            return ResponseResult.success(page);
        } catch (Exception e) {
            log.error("查询策略执行历史失败，策略ID: {}", id, e);
            return ResponseResult.fail("查询策略执行历史失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定展厅的启用策略
     * @param exhibitionId 展厅ID
     * @return 策略列表
     */
    @GetMapping("/exhibition/{exhibitionId}/enabled")
    public ResponseResult<List<Strategy>> getEnabledStrategies(@PathVariable Integer exhibitionId) {
        try {
            List<Strategy> strategies = strategyService.getEnabledStrategies(exhibitionId);
            return ResponseResult.success(strategies);
        } catch (Exception e) {
            log.error("查询启用策略失败，展厅ID: {}", exhibitionId, e);
            return ResponseResult.fail("查询启用策略失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定类型的启用策略
     * @param exhibitionId 展厅ID
     * @param type 策略类型
     * @return 策略列表
     */
    @GetMapping("/exhibition/{exhibitionId}/type/{type}/enabled")
    public ResponseResult<List<Strategy>> getEnabledStrategiesByType(
            @PathVariable Integer exhibitionId,
            @PathVariable Integer type) {
        try {
            List<Strategy> strategies = strategyService.getEnabledStrategiesByType(exhibitionId, type);
            return ResponseResult.success(strategies);
        } catch (Exception e) {
            log.error("查询指定类型启用策略失败，展厅ID: {}, 类型: {}", exhibitionId, type, e);
            return ResponseResult.fail("查询指定类型启用策略失败: " + e.getMessage());
        }
    }

    /**
     * 验证策略配置
     * @param type 策略类型
     * @param config 策略配置JSON
     * @return 验证结果
     */
    @PostMapping("/validate-config")
    public ResponseResult<Boolean> validateStrategyConfig(@RequestParam Integer type, @RequestBody String config) {
        try {
            boolean valid = strategyService.validateStrategyConfig(type, config);
            return ResponseResult.success(valid);
        } catch (Exception e) {
            log.error("验证策略配置失败，类型: {}", type, e);
            return ResponseResult.fail("验证策略配置失败: " + e.getMessage());
        }
    }
}
