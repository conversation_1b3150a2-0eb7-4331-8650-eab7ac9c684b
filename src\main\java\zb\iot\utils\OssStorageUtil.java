package zb.iot.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import zb.iot.config.OssConfig;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
public class OssStorageUtil {
    
    private final OSS ossClient;
    private final OssConfig ossConfig;
    
    /**
     * 上传文件到OSS
     * @param file 要上传的文件
     * @param folderPath OSS中的文件夹路径
     * @return 文件的访问URL
     */
    public String uploadFile(MultipartFile file, String folderPath) throws IOException {
        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new IOException("无法上传空文件");
        }
        
        // 生成文件路径，按日期分组
        String dateDir = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        // 示例 uuid.jpg
        String filename = UUID.randomUUID().toString() + extension;
        
        // 文件在OSS中的完整路径
        String objectName = folderPath + "/" + dateDir + "/" + filename;
        
        try {
            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(getContentType(extension));
            metadata.setContentLength(file.getSize());
            
            // 上传文件到OSS
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                    ossConfig.getBucketName(), 
                    objectName, 
                    file.getInputStream(),
                    metadata);
            
            ossClient.putObject(putObjectRequest);
            
            // 返回文件访问URL路径
            return ossConfig.getUrlPrefix() + objectName;
        } catch (Exception e) {
            log.error("上传文件到OSS失败: {}", e.getMessage());
            throw new IOException("上传文件到OSS失败: " + e.getMessage());
        }
    }

    /**
     * 处理OSS对象路径，转换为可访问的URL
     *
     * @param objectPath OSS对象路径或URL
     * @param expiration URL有效期(毫秒)，对于公共读取的bucket可以设为null
     * @return 可访问的URL字符串
     */
    public String getAccessUrl(String objectPath, Long expiration) {
        if (objectPath == null || objectPath.trim().isEmpty()) {
            return "";
        }

        try {
            // 1. 如果已经是完整的URL，且不需要签名，直接返回
            if (objectPath.startsWith("http://") || objectPath.startsWith("https://")) {
                if (expiration == null) {
                    return objectPath;
                }
                // 检查是否是当前bucket的URL
                String urlPrefix = ossConfig.getUrlPrefix();
                if (objectPath.startsWith(urlPrefix)) {
                    // 提取对象名
                    String objectName = objectPath.substring(urlPrefix.length());
                    return generatePresignedUrl(objectName, expiration);
                }
                return objectPath;
            }

            // 2. 如果是相对路径，需要处理
            // 如果需要签名(私有读取的bucket)
            if (expiration != null) {
                return generatePresignedUrl(objectPath, expiration);
            }

            // 公共读取的bucket，直接拼接URL
            return ossConfig.getUrlPrefix() + objectPath;
        } catch (Exception e) {
            log.error("处理OSS URL时出错: {}", e.getMessage(), e);
            return objectPath; // 出错时返回原始路径
        }
    }
    
    /**
     * 根据文件扩展名获取内容类型
     */
    private String getContentType(String extension) {
        if (extension == null) {
            return "application/octet-stream";
        }
        switch (extension.toLowerCase()) {
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".webp":
                return "image/webp";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 上传Logo图片到OSS
     */
    public String uploadLogo(MultipartFile file) throws IOException {
        return uploadFile(file, "IOT/logo");
    }

    /**
     * 生成带有签名的临时访问URL (用于私有bucket)
     */
    private String generatePresignedUrl(String objectName, Long expiration) {
        Date expirationDate = new Date(System.currentTimeMillis() + expiration);
        URL url = ossClient.generatePresignedUrl(ossConfig.getBucketName(), objectName, expirationDate);
        return url.toString();
    }

    /**
     * 获取公共读取的OSS对象URL
     */
    public String getPublicUrl(String objectPath) {
        return getAccessUrl(objectPath, null);
    }

    /**
     * 获取带签名的私有OSS对象URL，默认有效期1小时
     */
    public String getPrivateUrl(String objectPath) {
        return getAccessUrl(objectPath, 3600000L); // 1小时有效期
    }
}