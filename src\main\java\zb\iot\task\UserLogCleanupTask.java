package zb.iot.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import zb.iot.config.UserLogConfig;
import zb.iot.service.IUserOperationLogService;

import java.time.LocalDateTime;

/**
 * 用户日志清理定时任务
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "app.user-log", name = "cleanup-enabled", havingValue = "true", matchIfMissing = true)
public class UserLogCleanupTask {

    private final IUserOperationLogService userOperationLogService;
    private final UserLogConfig userLogConfig;

    /**
     * 定时清理过期日志
     * 默认每天凌晨2点执行
     */
    @Scheduled(cron = "${app.user-log.cleanup-cron:0 0 2 * * ?}")
    public void cleanupExpiredLogs() {
        log.info("开始执行用户日志清理任务");
        
        try {
            // 计算过期时间点
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(userLogConfig.getRetentionDays());
            
            // 执行清理
            int deletedCount = userOperationLogService.cleanExpiredLogs(beforeTime);
            
            log.info("用户日志清理任务完成，删除了 {} 条过期日志，保留天数: {}", 
                    deletedCount, userLogConfig.getRetentionDays());
                    
        } catch (Exception e) {
            log.error("用户日志清理任务执行失败", e);
        }
    }

    /**
     * 手动触发清理任务（用于测试）
     */
    public void manualCleanup() {
        log.info("手动触发用户日志清理任务");
        cleanupExpiredLogs();
    }

    /**
     * 清理指定天数前的日志
     * @param days 保留天数
     * @return 删除的记录数
     */
    public int cleanupLogsBefore(int days) {
        log.info("清理 {} 天前的用户日志", days);
        
        try {
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(days);
            int deletedCount = userOperationLogService.cleanExpiredLogs(beforeTime);
            
            log.info("清理完成，删除了 {} 条日志", deletedCount);
            return deletedCount;
            
        } catch (Exception e) {
            log.error("清理日志失败", e);
            return 0;
        }
    }

    /**
     * 获取日志统计信息
     */
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void logStatistics() {
        try {
            // 这里可以添加日志统计逻辑，比如：
            // - 统计当前日志总数
            // - 统计各类型日志数量
            // - 统计存储空间使用情况
            // - 发送统计报告等
            
            log.info("用户日志统计任务执行完成");
            
        } catch (Exception e) {
            log.error("用户日志统计任务执行失败", e);
        }
    }
}
