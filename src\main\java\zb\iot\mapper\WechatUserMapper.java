package zb.iot.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import zb.iot.entity.WechatUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 微信用户关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface WechatUserMapper extends BaseMapper<WechatUser> {
    
    /**
     * 通过OpenID查询微信用户关联信息
     * @param openId 微信OpenID
     * @return 微信用户关联信息
     */
    @Select("SELECT * FROM wechat_user WHERE open_id = #{openId}")
    WechatUser selectByOpenId(@Param("openId") String openId);
    
    /**
     * 通过UnionID查询微信用户关联信息
     * @param unionId 微信UnionID
     * @return 微信用户关联信息
     */
    @Select("SELECT * FROM wechat_user WHERE union_id = #{unionId}")
    WechatUser selectByUnionId(@Param("unionId") String unionId);
    
    /**
     * 通过用户ID查询微信用户关联信息
     * @param userId 用户ID
     * @return 微信用户关联信息
     */
    @Select("SELECT * FROM wechat_user WHERE user_id = #{userId}")
    WechatUser selectByUserId(@Param("userId") Integer userId);
} 