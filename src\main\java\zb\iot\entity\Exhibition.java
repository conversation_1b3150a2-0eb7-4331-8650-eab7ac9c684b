package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 展厅信息表
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
@TableName("exhibition")
public class Exhibition implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 展厅ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 展厅名称
     */
    @TableField("name")
    private String name;

    /**
     * 项目地址
     */
    @TableField("address")
    private String address;

    /**
     * 项目IP
     */
    @TableField("exhibition_ip")
    private String exhibitionIP;

    /**
     * 项目简介
     */
    @TableField("description")
    private String description;

    /**
     * Logo存储路径
     */
    @TableField("logo_path")
    private String logoPath;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 状态(1:启用,0:禁用)
     */
    @TableField("status")
    private Byte status;
}
