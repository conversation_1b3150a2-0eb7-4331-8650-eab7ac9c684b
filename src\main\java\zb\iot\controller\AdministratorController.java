package zb.iot.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import zb.iot.annotation.UserLog;
import zb.iot.common.ResponseResult;
import zb.iot.controller.dto.AddAdminRequest;
import zb.iot.controller.dto.AdminInfoDTO;
import zb.iot.entity.Administrator;
import zb.iot.service.IAdministratorService;

import java.util.List;

/**
 * 管理员表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@RestController
@RequestMapping("/administrator")
@RequiredArgsConstructor
@Slf4j
public class AdministratorController {

    private final IAdministratorService administratorService;

    /**
     * 添加管理员并分配权限
     *
     * @param request 包含展厅ID、用户ID和权限列表的请求
     * @return 添加结果
     */
    @PostMapping("/add")
    @UserLog(operationType = "CREATE", operationModule = "USER", description = "添加管理员")
    // @PreAuthorize("hasAuthority('ADMIN_MANAGE')")
    public ResponseResult<Administrator> addAdmin(@RequestBody AddAdminRequest request) {
        try {
            // 参数验证
            if (request.getExhibitionId() == null || request.getUserId() == null) {
                return ResponseResult.fail("展厅ID和用户ID不能为空");
            }

            // 添加管理员并分配权限
            Administrator admin = administratorService.addAdminWithPermissions(
                    request.getExhibitionId(),
                    request.getUserId(),
                    request.getPermissionIds()
            );

            return ResponseResult.success(admin);
        } catch (Exception e) {
            log.error("添加管理员失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 获取管理员详情，包括权限信息
     */
    @GetMapping("/{adminId}")
    @UserLog(operationType = "VIEW", operationModule = "USER", description = "查看管理员详情")
    // @PreAuthorize("hasAuthority('ADMIN_VIEW')")
    public ResponseResult<Administrator> getAdministrator(@PathVariable Integer adminId) {
        try {
            Administrator admin = administratorService.getAdministratorWithPermissions(adminId);
            if (admin == null) {
                return ResponseResult.fail("管理员不存在");
            }
            return ResponseResult.success(admin);
        } catch (Exception e) {
            log.error("获取管理员信息失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 更新管理员权限
     */
    @PutMapping("/{adminId}/permissions")
    @UserLog(operationType = "UPDATE", operationModule = "USER", description = "更新管理员权限")
    // @PreAuthorize("hasAuthority('ADMIN_MANAGE')")
    public ResponseResult<Administrator> updateAdministratorPermissions(
            @RequestParam Integer adminId, List<Integer> permissionIds) {
        try {
            Administrator admin = administratorService.updateAdministratorPermissions(
                    adminId, permissionIds);
            return ResponseResult.success(admin);
        } catch (Exception e) {
            log.error("更新管理员权限失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 根据展厅ID获取所有管理员
     */
    @GetMapping("/exhibition/{exhibitionId}")
    @UserLog(operationType = "VIEW", operationModule = "USER", description = "查看展厅管理员列表")
    // @PreAuthorize("hasAuthority('ADMIN_VIEW')")
    public ResponseResult<List<AdminInfoDTO>> getAdminsByExhibition(@PathVariable Integer exhibitionId) {
        try {
            List<AdminInfoDTO> admins = administratorService.getAdminsByExhibitionId(exhibitionId);
            log.info("获取展厅管理员成功: {}", admins);
            return ResponseResult.success(admins);
        } catch (Exception e) {
            log.error("获取展厅管理员失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }


    /**
     * 根据展厅ID获取非管理员用户
     * @param exhibitionId
     * @return
     */
    @GetMapping("/non-admin-users/{exhibitionId}")
    @UserLog(operationType = "VIEW", operationModule = "USER", description = "查看非管理员用户列表")
    public ResponseResult<List<AdminInfoDTO>> getNonAdminUsersByExhibitionId(
            @PathVariable Integer exhibitionId) {
        List<AdminInfoDTO> nonAdminUsers = administratorService.getNonAdminUsersByExhibitionId(exhibitionId);
        return ResponseResult.success(nonAdminUsers);
    }

}
