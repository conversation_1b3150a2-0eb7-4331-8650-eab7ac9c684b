package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户日志告警记录表
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
@Setter
@TableName("user_log_alert")
public class UserLogAlert implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 告警ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 告警类型
     */
    @TableField("alert_type")
    private String alertType;

    /**
     * 告警级别
     */
    @TableField("alert_level")
    private String alertLevel;

    /**
     * 告警标题
     */
    @TableField("alert_title")
    private String alertTitle;

    /**
     * 告警内容
     */
    @TableField("alert_content")
    private String alertContent;

    /**
     * 相关用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 相关用户名
     */
    @TableField("username")
    private String username;

    /**
     * 相关IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 触发次数
     */
    @TableField("trigger_count")
    private Integer triggerCount;

    /**
     * 告警状态
     */
    @TableField("alert_status")
    private String alertStatus;

    /**
     * 处理状态
     */
    @TableField("handle_status")
    private String handleStatus;

    /**
     * 处理人
     */
    @TableField("handler")
    private String handler;

    /**
     * 处理时间
     */
    @TableField("handle_time")
    private LocalDateTime handleTime;

    /**
     * 处理备注
     */
    @TableField("handle_remark")
    private String handleRemark;

    /**
     * 展厅ID
     */
    @TableField("exhibition_id")
    private Integer exhibitionId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // 告警类型常量
    public static final String ALERT_TYPE_LOGIN_FAILURE = "LOGIN_FAILURE";           // 登录失败
    public static final String ALERT_TYPE_SUSPICIOUS_IP = "SUSPICIOUS_IP";           // 可疑IP
    public static final String ALERT_TYPE_ERROR_OPERATION = "ERROR_OPERATION";       // 错误操作
    public static final String ALERT_TYPE_FREQUENT_OPERATION = "FREQUENT_OPERATION"; // 频繁操作

    // 告警级别常量
    public static final String ALERT_LEVEL_LOW = "LOW";         // 低级
    public static final String ALERT_LEVEL_MEDIUM = "MEDIUM";   // 中级
    public static final String ALERT_LEVEL_HIGH = "HIGH";       // 高级
    public static final String ALERT_LEVEL_CRITICAL = "CRITICAL"; // 严重

    // 告警状态常量
    public static final String ALERT_STATUS_ACTIVE = "ACTIVE";     // 活跃
    public static final String ALERT_STATUS_RESOLVED = "RESOLVED"; // 已解决
    public static final String ALERT_STATUS_IGNORED = "IGNORED";   // 已忽略

    // 处理状态常量
    public static final String HANDLE_STATUS_PENDING = "PENDING";       // 待处理
    public static final String HANDLE_STATUS_PROCESSING = "PROCESSING"; // 处理中
    public static final String HANDLE_STATUS_COMPLETED = "COMPLETED";   // 已完成
}
