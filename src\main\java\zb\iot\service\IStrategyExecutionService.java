package zb.iot.service;

import zb.iot.entity.Strategy;
import zb.iot.entity.StrategyExecutionLog;

/**
 * 策略执行服务接口
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface IStrategyExecutionService {

    /**
     * 执行策略
     * @param strategy 策略对象
     * @param triggerType 触发类型
     * @return 执行结果
     */
    StrategyExecutionLog executeStrategy(Strategy strategy, String triggerType);

    /**
     * 验证策略配置
     * @param type 策略类型
     * @param config 策略配置JSON
     * @return 验证结果
     */
    boolean validateStrategyConfig(Integer type, String config);

    /**
     * 执行群组策略
     * @param strategy 策略对象
     * @param triggerType 触发类型
     * @return 执行结果
     */
    StrategyExecutionLog executeGroupStrategy(Strategy strategy, String triggerType);

    /**
     * 执行定时策略
     * @param strategy 策略对象
     * @param triggerType 触发类型
     * @return 执行结果
     */
    StrategyExecutionLog executeScheduleStrategy(Strategy strategy, String triggerType);

    /**
     * 执行联动策略
     * @param strategy 策略对象
     * @param triggerType 触发类型
     * @return 执行结果
     */
    StrategyExecutionLog executeLinkageStrategy(Strategy strategy, String triggerType);
}
