package zb.iot.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 设备策略标签关联表
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
@TableName("device_police_tag")
public class DevicePolicyTag implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备id
     */
    @TableField("device_id")
    private Integer deviceId;

    /**
     * 策略标签id
     */
    @TableField("policy_id")
    private Integer policyId;


    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;
}
