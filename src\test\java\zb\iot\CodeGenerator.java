// package zb.iot;
// import com.baomidou.mybatisplus.generator.FastAutoGenerator;
// import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
//
// import java.nio.file.Paths;
//
//
// public class CodeGenerator {
//
//     public static void main(String[] args) {
//         FastAutoGenerator.create("*******************************************************************************************************************************", "root", "12345678")
//                 .globalConfig(builder -> builder
//                         .author("zjw")
//                         .outputDir(Paths.get(System.getProperty("user.dir")) + "/src/main/java")
//                 )
//                 .packageConfig(builder -> builder
//                         .parent("zb.iot")
//                         .entity("entity")
//                         .mapper("mapper")
//                         .service("service")
//                         .serviceImpl("service.impl")
//                         .xml("mapper"))
//                 .strategyConfig(builder -> builder
//                         .addInclude("admin_permission","administrator", "device", "exhibition", "permission", "schedule", "user", "video")
//                         .entityBuilder()
//                         .enableLombok()
//                         .enableTableFieldAnnotation()
//                         .controllerBuilder()
//                         .enableRestStyle()
//                         .enableFileOverride())
//                 .templateEngine(new FreemarkerTemplateEngine())
//                 .execute();
//     }
// }