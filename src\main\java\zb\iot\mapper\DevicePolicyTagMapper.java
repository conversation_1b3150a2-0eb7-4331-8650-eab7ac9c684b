package zb.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import zb.iot.entity.DevicePolicyTag;
import zb.iot.entity.PolicyTag;

import java.util.List;

public interface DevicePolicyTagMapper extends BaseMapper<DevicePolicyTag> {

    /**
     * 根据设备ID获取策略标签列表
     * @param deviceId 设备ID
     * @return 策略标签列表
     */
    @Select("SELECT pt.id, pt.tag_name " +
            "FROM policy_tag pt " +
            "INNER JOIN device_police_tag dpt ON pt.id = dpt.policy_id " +
            "WHERE dpt.device_id = #{deviceId} AND dpt.deleted = 0")
    List<PolicyTag> getPolicyTagIdsByDeviceId(@Param("deviceId") Integer deviceId);

    /**
     * 根据设备ID删除策略标签关联（逻辑删除）
     * @param deviceId 设备ID
     */
    @Delete("UPDATE device_police_tag SET deleted = 1 WHERE device_id = #{deviceId}")
    void deleteByDeviceId(@Param("deviceId") Integer deviceId);

    /**
     * 批量插入设备策略标签关联
     * @param relations 关联列表
     */
    void batchInsert(@Param("list") List<DevicePolicyTag> relations);
}
