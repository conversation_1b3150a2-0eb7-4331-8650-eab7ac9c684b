package zb.iot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import zb.iot.annotation.UserLog;
import zb.iot.common.ResponseResult;
import zb.iot.entity.DeviceType;
import zb.iot.service.IDeviceTypeService;

import java.util.List;

/**
 * 设备类型表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("/deviceTypes")
@RequiredArgsConstructor
public class DeviceTypeController {

    private final IDeviceTypeService deviceTypeService;

    /**
     * 添加设备类型
     *
     * @param deviceType 设备类型信息
     * @return 添加结果
     */
    @PostMapping("/add")
    @UserLog(operationType = "CREATE", operationModule = "DEVICE", description = "添加设备类型")
    public ResponseResult<String> addDeviceType(@Validated @RequestBody DeviceType deviceType) {
        try {
            boolean result = deviceTypeService.addDeviceType(deviceType);
            if (result) {
                return ResponseResult.success();
            } else {
                return ResponseResult.fail("设备类型添加失败");
            }
        } catch (Exception e) {
            log.error("添加设备类型失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 更新设备类型
     *
     * @param deviceType 设备类型信息
     * @return 更新结果
     */
    @PostMapping("/update")
    @UserLog(operationType = "UPDATE", operationModule = "DEVICE", description = "更新设备类型")
    public ResponseResult<String> updateDeviceType(@Validated @RequestBody DeviceType deviceType) {
        try {
            boolean result = deviceTypeService.updateDeviceType(deviceType);
            if (result) {
                return ResponseResult.success();
            } else {
                return ResponseResult.fail("设备类型更新失败");
            }
        } catch (Exception e) {
            log.error("更新设备类型失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 删除设备类型
     *
     * @param id 设备类型ID
     * @return 删除结果
     */
    @DeleteMapping("/del/{id}")
    @UserLog(operationType = "DELETE", operationModule = "DEVICE", description = "删除设备类型")
    public ResponseResult<String> deleteDeviceType(@PathVariable Integer id) {
        try {
            boolean result = deviceTypeService.deleteDeviceType(id);
            if (result) {
                return ResponseResult.success();
            } else {
                return ResponseResult.fail("设备类型删除失败，设备类型可能不存在");
            }
        } catch (Exception e) {
            log.error("删除设备类型失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 获取设备类型详情
     *
     * @param id 设备类型ID
     * @return 设备类型详情
     */
    @GetMapping("/{id}")
    public ResponseResult<DeviceType> getDeviceType(@PathVariable Integer id) {
        try {
            DeviceType deviceType = deviceTypeService.getDeviceTypeById(id);
            if (deviceType != null) {
                return ResponseResult.success(deviceType);
            } else {
                return ResponseResult.fail("设备类型不存在");
            }
        } catch (Exception e) {
            log.error("获取设备类型详情失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 分页查询设备类型
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 设备类型名称（可选，用于模糊查询）
     * @param exhibitionId 展厅ID（可选）
     * @return 分页设备类型列表
     */
    @GetMapping("/page/{exhibitionId}")
    public ResponseResult<IPage<DeviceType>> pageDeviceTypes(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @PathVariable Integer exhibitionId) {
        try {
            IPage<DeviceType> deviceTypePage = deviceTypeService.pageDeviceTypes(pageNum, pageSize, name, exhibitionId);
            return ResponseResult.success(deviceTypePage);
        } catch (Exception e) {
            log.error("分页查询设备类型失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }


    /**
     * 发布设备类型
     *
     * @param id 设备类型ID
     * @return 发布结果
     */
    @PostMapping("/publish/{id}")
    @UserLog(operationType = "UPDATE", operationModule = "DEVICE", description = "发布设备类型")
    public ResponseResult<String> publishDeviceType(@PathVariable Integer id) {
        try {
            boolean result = deviceTypeService.publishDeviceType(id);
            if (result) {
                return ResponseResult.success("设备类型发布成功");
            } else {
                return ResponseResult.fail("设备类型发布失败");
            }
        } catch (Exception e) {
            log.error("发布设备类型失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 取消发布设备类型
     *
     * @param id 设备类型ID
     * @return 取消发布结果
     */
    @PostMapping("/unpublish/{id}")
    @UserLog(operationType = "UPDATE", operationModule = "DEVICE", description = "取消发布设备类型")
    public ResponseResult<String> unpublishDeviceType(@PathVariable Integer id) {
        try {
            boolean result = deviceTypeService.unpublishDeviceType(id);
            if (result) {
                return ResponseResult.success("设备类型取消发布成功");
            } else {
                return ResponseResult.fail("设备类型取消发布失败");
            }
        } catch (Exception e) {
            log.error("取消发布设备类型失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
    
    /**
     * 获取所有已发布的设备类型
     *
     * @param exhibitionId 展厅ID（可选）
     * @return 已发布的设备类型列表
     */
    @GetMapping("/published")
    public ResponseResult<List<DeviceType>> getPublishedDeviceTypes(
            @RequestParam Integer exhibitionId) {
        try {
            List<DeviceType> publishedDeviceTypes = deviceTypeService.getPublishedDeviceTypes(exhibitionId);
            return ResponseResult.success(publishedDeviceTypes);
        } catch (Exception e) {
            log.error("获取已发布设备类型失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
}
