package zb.iot.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zb.iot.entity.User;
import zb.iot.entity.WechatUser;
import zb.iot.mapper.UserMapper;
import zb.iot.mapper.WechatUserMapper;
import zb.iot.service.IWechatService;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class WechatServiceImpl implements IWechatService {

    private final WechatUserMapper wechatUserMapper;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    
    // 配置信息应从配置文件中读取
    @Value("${wechat.appid:}")
    private String appId;
    
    @Value("${wechat.secret:}")
    private String appSecret;

    @Override
    public Map<String, Object> getWechatUserInfo(String code) {
        // 实际项目中，这里应该调用微信API，使用code换取access_token和openid
        // 然后使用access_token和openid获取用户信息
        // 这里仅作为示例，返回一个模拟的用户信息
        
        log.info("获取微信用户信息，授权码: {}", code);
        
        // 模拟微信API调用
        // String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + appId + "&secret=" + appSecret + "&code=" + code + "&grant_type=authorization_code";
        // String result = HttpUtil.get(url);
        // Map<String, Object> tokenInfo = JSON.parseObject(result);
        
        // 使用access_token和openid获取用户信息
        // String userInfoUrl = "https://api.weixin.qq.com/sns/userinfo?access_token=" + tokenInfo.get("access_token") + "&openid=" + tokenInfo.get("openid") + "&lang=zh_CN";
        // String userInfo = HttpUtil.get(userInfoUrl);
        // return JSON.parseObject(userInfo);
        
        // 模拟返回用户信息
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("openid", "wx_" + code + "_" + RandomUtil.randomString(10));
        userInfo.put("unionid", "unionid_" + RandomUtil.randomString(10));
        userInfo.put("nickname", "微信用户_" + RandomUtil.randomString(6));
        userInfo.put("headimgurl", "https://example.com/avatar.jpg");
        
        return userInfo;
    }

    @Override
    @Transactional
    public User getUserByWechatOpenId(String openId, String unionId, Map<String, Object> wechatInfo) {
        // 先通过OpenID查询
        WechatUser wechatUser = wechatUserMapper.selectByOpenId(openId);
        
        // 如果未找到，则尝试通过UnionID查询
        if (wechatUser == null && unionId != null) {
            wechatUser = wechatUserMapper.selectByUnionId(unionId);
        }
        
        // 如果找到了关联记录，则返回对应的用户
        if (wechatUser != null) {
            User user = userMapper.selectById(wechatUser.getUserId());
            if (user != null) {
                return user;
            }
        }
        
        // 如果未找到关联用户，则创建新用户
        String nickname = wechatInfo.get("nickname") != null ? wechatInfo.get("nickname").toString() : "微信用户_" + RandomUtil.randomString(6);
        
        User newUser = new User();
        newUser.setUsername(nickname);
        newUser.setPassword(passwordEncoder.encode(RandomUtil.randomString(20))); // 随机密码
        newUser.setRegisterTime(LocalDateTime.now());
        newUser.setStatus((byte)1);
        
        userMapper.insert(newUser);
        
        // 创建微信用户关联
        linkWechatUser(newUser.getId(), openId, unionId, wechatInfo);
        
        return newUser;
    }

    @Override
    @Transactional
    public WechatUser linkWechatUser(Integer userId, String openId, String unionId, Map<String, Object> wechatInfo) {
        // 检查是否已存在关联
        WechatUser existingUser = wechatUserMapper.selectByUserId(userId);
        if (existingUser != null) {
            // 如果已存在关联，则更新信息
            existingUser.setOpenId(openId);
            existingUser.setUnionId(unionId);
            existingUser.setNickname(wechatInfo.get("nickname") != null ? wechatInfo.get("nickname").toString() : null);
            existingUser.setAvatarUrl(wechatInfo.get("headimgurl") != null ? wechatInfo.get("headimgurl").toString() : null);
            existingUser.setUpdateTime(LocalDateTime.now());
            
            wechatUserMapper.updateById(existingUser);
            return existingUser;
        }
        
        // 创建新的关联记录
        WechatUser wechatUser = new WechatUser();
        wechatUser.setUserId(userId);
        wechatUser.setOpenId(openId);
        wechatUser.setUnionId(unionId);
        wechatUser.setNickname(wechatInfo.get("nickname") != null ? wechatInfo.get("nickname").toString() : null);
        wechatUser.setAvatarUrl(wechatInfo.get("headimgurl") != null ? wechatInfo.get("headimgurl").toString() : null);
        wechatUser.setCreateTime(LocalDateTime.now());
        wechatUser.setUpdateTime(LocalDateTime.now());
        
        wechatUserMapper.insert(wechatUser);
        return wechatUser;
    }
} 