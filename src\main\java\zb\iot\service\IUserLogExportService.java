package zb.iot.service;

import jakarta.servlet.http.HttpServletResponse;
import zb.iot.controller.dto.UserLogQueryDTO;

import java.util.List;

/**
 * 用户日志导出服务接口
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface IUserLogExportService {

    /**
     * 导出用户日志到Excel
     * @param queryDTO 查询条件
     * @param response HTTP响应
     */
    void exportToExcel(UserLogQueryDTO queryDTO, HttpServletResponse response);

    /**
     * 批量导出指定ID的日志
     * @param logIds 日志ID列表
     * @param response HTTP响应
     */
    void batchExportToExcel(List<Long> logIds, HttpServletResponse response);

    /**
     * 导出日志统计报告
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param response HTTP响应
     */
    void exportStatisticsReport(Integer exhibitionId, String startTime, String endTime, HttpServletResponse response);

    /**
     * 生成Excel文件并返回下载URL
     * @param queryDTO 查询条件
     * @return 下载URL
     */
    String generateExcelFile(UserLogQueryDTO queryDTO);
}
