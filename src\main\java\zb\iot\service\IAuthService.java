package zb.iot.service;

import zb.iot.controller.dto.AuthResponse;
import zb.iot.controller.dto.LoginRequest;
import zb.iot.controller.dto.RegisterRequest;
import zb.iot.controller.dto.WechatLoginRequest;

public interface IAuthService {
    
    /**
     * 用户登录（通过用户名/邮箱/手机号和密码）
     * @param request 登录请求
     * @return 登录结果
     */
    AuthResponse login(LoginRequest request);
    
    /**
     * 用户注册
     * @param request 注册请求
     * @return 注册结果
     */
    Boolean register(RegisterRequest request);
    
    /**
     * 微信登录
     * @param request 微信登录请求
     * @return 登录结果
     */
    AuthResponse wechatLogin(WechatLoginRequest request);
} 