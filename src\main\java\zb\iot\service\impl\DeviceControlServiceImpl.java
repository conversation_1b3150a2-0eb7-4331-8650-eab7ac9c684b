package zb.iot.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import zb.iot.entity.Device;
import zb.iot.service.IDeviceControlService;
import zb.iot.service.IDeviceService;
import zb.iot.service.IUdpService;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备控制服务实现类
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceControlServiceImpl implements IDeviceControlService {

    private final IDeviceService deviceService;
    private final IUdpService udpService;

    @Override
    public DeviceControlResult executeDeviceAction(Integer deviceId, String action) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取设备信息
            Device device = deviceService.getById(deviceId);
            if (device == null) {
                return new DeviceControlResult(false, "设备不存在", null, 
                    System.currentTimeMillis() - startTime);
            }

            // 根据协议类型执行操作
            boolean success = executeByProtocol(device, action);
            String message = success ? "操作成功" : "操作失败";
            
            return new DeviceControlResult(success, message, device, 
                System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("执行设备操作异常，设备ID: {}, 动作: {}", deviceId, action, e);
            return new DeviceControlResult(false, "操作异常: " + e.getMessage(), null, 
                System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public BatchDeviceControlResult batchExecuteDeviceAction(Integer[] deviceIds, String action) {
        List<DeviceControlResult> results = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        for (Integer deviceId : deviceIds) {
            DeviceControlResult result = executeDeviceAction(deviceId, action);
            results.add(result);
            
            if (result.isSuccess()) {
                successCount++;
            } else {
                failureCount++;
            }
        }

        return new BatchDeviceControlResult(deviceIds.length, successCount, failureCount, 
            results.toArray(new DeviceControlResult[0]));
    }

    @Override
    public boolean testDeviceConnection(Integer deviceId) {
        try {
            Device device = deviceService.getById(deviceId);
            if (device == null) {
                return false;
            }

            // 根据协议类型测试连接
            Integer protocolTypeId = device.getProtocolTypeId();
            if (protocolTypeId == null) {
                return false;
            }

            switch (protocolTypeId) {
                case 1: // UDP协议
                    return testUdpConnection(device);
                case 2: // LAN唤醒
                    return testLanConnection(device);
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("测试设备连接异常，设备ID: {}", deviceId, e);
            return false;
        }
    }

    /**
     * 根据协议类型执行设备操作
     */
    private boolean executeByProtocol(Device device, String action) {
        Integer protocolTypeId = device.getProtocolTypeId();
        if (protocolTypeId == null) {
            log.error("设备协议类型未设置，设备ID: {}", device.getId());
            return false;
        }

        log.info("执行设备操作，设备ID: {}, 协议类型: {}, 动作: {}", 
            device.getId(), protocolTypeId, action);

        switch (protocolTypeId) {
            case 1: // UDP协议
                return executeUdpAction(device, action);
            case 2: // LAN唤醒
                return executeLanWakeAction(device, action);
            default:
                log.error("不支持的协议类型: {}, 设备ID: {}", protocolTypeId, device.getId());
                return false;
        }
    }

    /**
     * 执行UDP协议操作
     */
    private boolean executeUdpAction(Device device, String action) {
        try {
            String ipAddress = device.getIpAddress();
            Integer port = device.getPort();

            if (ipAddress == null || ipAddress.trim().isEmpty()) {
                log.error("设备IP地址为空，设备ID: {}", device.getId());
                return false;
            }

            if (port == null || port <= 0) {
                log.error("设备端口无效，设备ID: {}, 端口: {}", device.getId(), port);
                return false;
            }

            String command = getDeviceCommand(device, action);
            if (command == null || command.trim().isEmpty()) {
                log.error("设备命令为空，设备ID: {}, 动作: {}", device.getId(), action);
                return false;
            }

            log.info("发送UDP命令，设备: {}({}:{}), 命令: {}", 
                device.getName(), ipAddress, port, command);

            return udpService.sendDeviceHexCommand(ipAddress, port, command);

        } catch (Exception e) {
            log.error("执行UDP操作异常，设备ID: {}", device.getId(), e);
            return false;
        }
    }

    /**
     * 执行LAN唤醒操作
     */
    private boolean executeLanWakeAction(Device device, String action) {
        try {
            if (!"power_on".equals(action) && !"wake".equals(action)) {
                log.warn("LAN唤醒不支持操作: {}, 设备ID: {}", action, device.getId());
                return false;
            }

            String macAddress = device.getMacAddress();
            if (macAddress == null || macAddress.trim().isEmpty()) {
                log.error("设备MAC地址为空，设备ID: {}", device.getId());
                return false;
            }

            return sendWakeOnLanPacket(device.getIpAddress(), macAddress);

        } catch (Exception e) {
            log.error("执行LAN唤醒异常，设备ID: {}", device.getId(), e);
            return false;
        }
    }

    /**
     * 测试UDP连接
     */
    private boolean testUdpConnection(Device device) {
        try {
            String ipAddress = device.getIpAddress();
            Integer port = device.getPort();

            if (ipAddress == null || port == null || port <= 0) {
                return false;
            }

            // 发送测试命令或ping
            return udpService.testConnection(ipAddress, port);
        } catch (Exception e) {
            log.error("测试UDP连接异常", e);
            return false;
        }
    }

    /**
     * 测试LAN连接
     */
    private boolean testLanConnection(Device device) {
        try {
            String ipAddress = device.getIpAddress();
            if (ipAddress == null) {
                return false;
            }

            // 简单的ping测试
            return udpService.pingHost(ipAddress);
        } catch (Exception e) {
            log.error("测试LAN连接异常", e);
            return false;
        }
    }

    /**
     * 获取设备命令
     */
    private String getDeviceCommand(Device device, String action) {
        switch (action) {
            case "power_on":
                return device.getEnableCommand();
            case "power_off":
                return device.getDisableCommand();
            case "restart":
                return device.getDisableCommand(); // 简化处理
            default:
                log.warn("未知动作: {}", action);
                return null;
        }
    }

    /**
     * 发送LAN唤醒数据包
     */
    private boolean sendWakeOnLanPacket(String ipAddress, String macAddress) {
        try {
            String cleanMac = macAddress.replaceAll("[:-]", "");
            if (cleanMac.length() != 12) {
                log.error("MAC地址格式错误: {}", macAddress);
                return false;
            }

            StringBuilder magicPacket = new StringBuilder();
            for (int i = 0; i < 6; i++) {
                magicPacket.append("FF");
            }
            for (int i = 0; i < 16; i++) {
                magicPacket.append(cleanMac);
            }

            String broadcastAddress = getBroadcastAddress(ipAddress);
            return udpService.sendHexMessage(broadcastAddress, 9, magicPacket.toString());

        } catch (Exception e) {
            log.error("发送Magic Packet异常", e);
            return false;
        }
    }

    /**
     * 获取广播地址
     */
    private String getBroadcastAddress(String ipAddress) {
        try {
            String[] parts = ipAddress.split("\\.");
            if (parts.length == 4) {
                return parts[0] + "." + parts[1] + "." + parts[2] + ".255";
            }
        } catch (Exception e) {
            log.error("计算广播地址失败", e);
        }
        return "***************";
    }
}
