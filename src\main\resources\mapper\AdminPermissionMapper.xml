<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zb.iot.mapper.AdminPermissionMapper">


    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO admin_permission (admin_id, permission_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.adminId}, #{item.permissionId}, #{item.createTime})
        </foreach>
    </insert>

    <delete id="deleteByAdminId">
        DELETE FROM admin_permission WHERE admin_id = #{adminId}
    </delete>

</mapper>
