package zb.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.multipart.MultipartFile;
import zb.iot.controller.dto.ExhibitionDTO;
import zb.iot.entity.Exhibition;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 展厅信息表 服务类
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface IExhibitionService extends IService<Exhibition> {

    IPage<ExhibitionDTO> getExhibitionPage(Integer pageNum, Integer pageSize, String name);

    boolean addExhibition(ExhibitionDTO exhibitionDTO, MultipartFile logo);

    boolean updateExhibition(Exhibition exhibition);

    boolean removeExhibitionById(Integer id);

}
