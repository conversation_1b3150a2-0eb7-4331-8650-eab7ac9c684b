package zb.iot.controller.dto;

import lombok.Data;
import zb.iot.entity.Exhibition;

import java.time.LocalDateTime;

@Data
public class ExhibitionDTO {

    private Integer id;
    private String name;
    private String logo;
    private String description;
    private String address;
    private String exhibitionIP;
    private LocalDateTime createTime;
    
    // 从实体类转换为DTO
    public static ExhibitionDTO fromEntity(Exhibition exhibition, String logo) {
        ExhibitionDTO dto = new ExhibitionDTO();

        dto.setId(exhibition.getId());
        dto.setName(exhibition.getName());
        dto.setLogo(logo);
        dto.setDescription(exhibition.getDescription());
        dto.setAddress(exhibition.getAddress());
        dto.setCreateTime(exhibition.getCreateTime());
        return dto;
    }
}