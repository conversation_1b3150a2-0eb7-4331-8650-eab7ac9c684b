package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * 权限表
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
@TableName("permission")
public class Permission implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 权限ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 权限名称
     */
    @TableField("name")
    private String name;

    /**
     * 权限描述
     */
    @TableField("description")
    private String description;

    /**
     * 权限代码
     */
    @TableField("code")
    private String code;
}
