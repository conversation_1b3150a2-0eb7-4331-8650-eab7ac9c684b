package zb.iot.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import zb.iot.annotation.UserLog;
import zb.iot.entity.UserOperationLog;
import zb.iot.service.IUserOperationLogService;
import zb.iot.utils.UserContextUtils;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 用户操作日志切面
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class UserLogAspect {

    private final IUserOperationLogService userOperationLogService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 定义切点：所有带有@UserLog注解的方法
     */
    @Pointcut("@annotation(zb.iot.annotation.UserLog)")
    public void userLogPointcut() {}

    /**
     * 方法执行成功后记录日志
     */
    @AfterReturning(pointcut = "userLogPointcut()", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, Object result) {
        handleUserLog(joinPoint, null, result);
    }

    /**
     * 方法执行异常后记录日志
     */
    @AfterThrowing(pointcut = "userLogPointcut()", throwing = "exception")
    public void doAfterThrowing(JoinPoint joinPoint, Exception exception) {
        handleUserLog(joinPoint, exception, null);
    }

    /**
     * 处理用户日志记录
     */
    private void handleUserLog(JoinPoint joinPoint, Exception exception, Object result) {
        try {
            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            UserLog userLog = method.getAnnotation(UserLog.class);

            if (userLog == null) {
                return;
            }

            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return;
            }

            HttpServletRequest request = attributes.getRequest();

            // 获取用户信息
            Integer userId = UserContextUtils.getCurrentUserId();
            String username = UserContextUtils.getCurrentUsername();

            // 获取请求参数
            String requestParams = null;
            if (userLog.recordParams()) {
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    try {
                        // 尝试将参数序列化为JSON数组格式，避免多个JSON对象连接导致的格式错误
                        requestParams = objectMapper.writeValueAsString(args);
                    } catch (Exception e) {
                        // 如果整体序列化失败，则逐个序列化参数
                        requestParams = Arrays.stream(args)
                                .map(arg -> {
                                    try {
                                        // 尝试对每个参数单独序列化
                                        return objectMapper.writeValueAsString(arg);
                                    } catch (Exception innerException) {
                                        // 如果序列化失败，返回对象的字符串表示
                                        return arg != null ? arg.toString() : "null";
                                    }
                                })
                                .collect(Collectors.joining(", ", "[", "]"));
                    }
                }
            }

            // 确定响应状态
            String responseStatus;
            String errorMessage = null;
            if (exception != null) {
                responseStatus = UserOperationLog.STATUS_ERROR;
                errorMessage = exception.getMessage();
            } else {
                responseStatus = UserOperationLog.STATUS_SUCCESS;
            }

            // 获取操作详情
            String operationDetail = userLog.description();
            if (operationDetail.isEmpty()) {
                operationDetail = method.getName();
            }

            // 获取展厅ID
            Integer exhibitionId = UserContextUtils.getCurrentExhibitionId();

            // 记录日志
            userOperationLogService.recordUserOperation(
                    userId,
                    username,
                    userLog.operationType(),
                    userLog.operationModule(),
                    operationDetail,
                    extractTargetId(joinPoint.getArgs()),
                    extractTargetName(joinPoint.getArgs()),
                    UserContextUtils.getCurrentUserIp(),
                    UserContextUtils.getCurrentUserAgent(),
                    request.getRequestURL().toString(),
                    request.getMethod(),
                    requestParams,
                    responseStatus,
                    errorMessage,
                    null, // 执行耗时在这里无法准确计算，可以在拦截器中处理
                    exhibitionId
            );

        } catch (Exception e) {
            log.error("记录用户操作日志失败", e);
        }
    }



    /**
     * 从方法参数中提取目标ID
     */
    private String extractTargetId(Object[] args) {
        if (args != null && args.length > 0) {
            Object firstArg = args[0];
            if (firstArg instanceof Number) {
                return firstArg.toString();
            }
        }
        return null;
    }

    /**
     * 从方法参数中提取目标名称
     */
    private String extractTargetName(Object[] args) {
        // 这里可以根据具体的业务逻辑来提取目标名称
        return null;
    }


}
