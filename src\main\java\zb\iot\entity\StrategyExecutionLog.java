package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 策略执行记录表
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
@Setter
@TableName("strategy_execution_log")
public class StrategyExecutionLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 策略ID
     */
    @TableField("strategy_id")
    private Integer strategyId;

    /**
     * 执行时间
     */
    @TableField("execution_time")
    private LocalDateTime executionTime;

    /**
     * 执行状态(0:失败,1:成功,2:部分成功)
     */
    @TableField("status")
    private Integer status;

    /**
     * 执行结果详情
     */
    @TableField("result")
    private String result;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 执行耗时(毫秒)
     */
    @TableField("execution_duration")
    private Integer executionDuration;

    /**
     * 影响的设备列表(JSON格式)
     */
    @TableField("affected_devices")
    private String affectedDevices;

    /**
     * 触发类型(manual:手动,scheduled:定时,linkage:联动)
     */
    @TableField("trigger_type")
    private String triggerType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    // 执行状态常量
    public static final int STATUS_FAILED = 0;         // 失败
    public static final int STATUS_SUCCESS = 1;        // 成功
    public static final int STATUS_PARTIAL_SUCCESS = 2; // 部分成功

    // 触发类型常量
    public static final String TRIGGER_MANUAL = "manual";       // 手动触发
    public static final String TRIGGER_SCHEDULED = "scheduled"; // 定时触发
    public static final String TRIGGER_LINKAGE = "linkage";     // 联动触发
}
