package zb.iot.security;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import zb.iot.entity.Administrator;
import zb.iot.entity.User;
import zb.iot.mapper.AdministratorMapper;
import zb.iot.mapper.UserMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;


@Service
@RequiredArgsConstructor
@Slf4j
public class CustomUserDetailsService implements UserDetailsService {

    private final UserMapper userMapper;
    private final AdministratorMapper administratorMapper;

    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@(.+)$");
    // 手机号正则表达式（简化版，实际应根据国家地区有所不同）
    private static final Pattern PHONE_PATTERN = Pattern.compile("^\\d{11}$");

    @Override
    public UserDetails loadUserByUsername(String userIdentifier) throws UsernameNotFoundException {
        User user = null;

        // 根据输入的标识类型查询用户
        if (EMAIL_PATTERN.matcher(userIdentifier).matches()) {
            // 输入的是邮箱
            user = userMapper.selectByEmail(userIdentifier);
        } else if (PHONE_PATTERN.matcher(userIdentifier).matches()) {
            // 输入的是手机号
            user = userMapper.selectByPhone(userIdentifier);
        } else {
            // 当作用户名处理
            user = userMapper.selectByUsername(userIdentifier);
        }

        if (user == null) {
            throw new UsernameNotFoundException("未找到用户: " + userIdentifier);
        }

        List<Integer> adminPermissions = getAdminPermissions(user.getId());
        
        return CustomUserDetails.fromUser(user, adminPermissions);
    }

    /**
     * 获取用户的管理员权限
     * @param userId 用户ID
     * @return 权限ID列表，如果不是管理员则返回null
     */
    private List<Integer> getAdminPermissions(Integer userId) {
        // 查询用户是否为管理员
        Integer adminId = administratorMapper.getAdminIdByUserId(userId);
        if (adminId == null) {
            // 用户不是管理员，返回null
            return null;
        }
        Administrator admin = administratorMapper.getAdminWithPermissions(adminId);
        if (admin.getPermissions() == null || admin.getPermissions().isEmpty()) {
            log.warn("管理员ID为{}的用户没有分配权限", adminId);
            return new ArrayList<>(); // 返回空列表
        }
        // 用户是管理员，查询其拥有的权限
        return admin.getPermissions();
    }
} 