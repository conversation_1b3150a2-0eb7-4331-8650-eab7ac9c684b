package zb.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import zb.iot.entity.PolicyTag;

import java.util.List;

/**
 * 标签表
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface IPolicyTagService extends IService<PolicyTag> {

    /**
     * 添加标签
     *
     * @param PolicyTag 标签
     * @return 添加的标签
     */
    boolean addTag(PolicyTag policyTag);

    /**
     * 更新标签
     *
     * @param tagId 标签ID
     * @param name 标签名称
     * @param description 标签描述
     * @return 更新后的标签
     */
    boolean updateTag(Integer tagId, String name, String description);

    /**
     * 批量删除标签
     *
     * @param tagIds 标签ID列表
     * @return 成功删除的数量
     */
    boolean batchDeleteTags(List<Integer> tagIds);

    /**
     * 分页查询标签列表
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 标签名称(可选，模糊查询)
     * @return 分页标签列表
     */
    IPage<PolicyTag> pageTags(Integer pageNum, Integer pageSize, String name, Integer exhibitionId);

    /**
     * 更新标签
     *
     * @param tagId 标签ID
     * @param status 标签状态
     * @return 更新后的标签
     */
    boolean updateStatus(Integer tagId, Byte status);

    List<PolicyTag> getAllByExhibition(Integer exhibitionId);
}
