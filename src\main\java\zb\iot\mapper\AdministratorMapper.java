package zb.iot.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import zb.iot.controller.dto.AdminInfoDTO;
import zb.iot.entity.Administrator;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 管理员表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface AdministratorMapper extends BaseMapper<Administrator> {

    /**
     * 根据ID获取管理员，并加载关联的权限
     * 此方法通过XML映射实现
     */
    Administrator getAdminWithPermissions(@Param("adminId") Integer adminId);

    /**
     * 根据展厅ID获取所有管理员列表
     *
     * @param exhibitionId 展厅ID
     * @return 管理员信息列表
     */
    List<AdminInfoDTO> getAdminsByExhibitionId(@Param("exhibitionId") Integer exhibitionId);

    /**
     * 根据用户ID获取管理员ID
     * @param userId 用户ID
     * @return 管理员ID，如果不是管理员则返回null
     */
    Integer getAdminIdByUserId(@Param("userId") Integer userId);

    /**
     * 根据展厅ID获取所有非管理员用户列表
     *
     * @param exhibitionId 展厅ID
     * @return 非管理员用户信息列表
     */
    List<AdminInfoDTO> getNonAdminUsersByExhibitionId(@Param("exhibitionId") Integer exhibitionId);

}
