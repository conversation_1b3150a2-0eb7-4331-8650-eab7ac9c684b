package zb.iot.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import zb.iot.controller.dto.UserLogQueryDTO;
import zb.iot.entity.UserOperationLog;
import zb.iot.entity.VO.UserOperationLogVO;
import zb.iot.mapper.UserOperationLogMapper;
import zb.iot.service.IUserOperationLogService;
import zb.iot.service.IUserLogAlertService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户操作日志服务实现类
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserOperationLogServiceImpl extends ServiceImpl<UserOperationLogMapper, UserOperationLog> implements IUserOperationLogService {

    private final UserOperationLogMapper userOperationLogMapper;
    private final IUserLogAlertService userLogAlertService;

    @Override
    public IPage<UserOperationLogVO> getUserLogPage(UserLogQueryDTO queryDTO) {
        Page<UserOperationLogVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        return userOperationLogMapper.selectUserLogPage(page, queryDTO);
    }

    @Override
    @Async
    public void recordUserOperation(Integer userId, String username, String operationType, String operationModule,
                                  String operationDetail, String targetId, String targetName, String ipAddress,
                                  String userAgent, String requestUrl, String requestMethod, String requestParams,
                                  String responseStatus, String errorMessage, Integer executionTime, Integer exhibitionId) {
        try {
            UserOperationLog operationLog = new UserOperationLog();
            operationLog.setUserId(userId);
            operationLog.setUsername(username);
            operationLog.setOperationType(operationType);
            operationLog.setOperationModule(operationModule);
            operationLog.setOperationDetail(operationDetail);
            operationLog.setTargetId(targetId);
            operationLog.setTargetName(targetName);
            operationLog.setIpAddress(ipAddress);
            operationLog.setUserAgent(userAgent);
            operationLog.setRequestUrl(requestUrl);
            operationLog.setRequestMethod(requestMethod);
            operationLog.setRequestParams(requestParams);
            operationLog.setResponseStatus(responseStatus);
            operationLog.setErrorMessage(errorMessage);
            operationLog.setExecutionTime(executionTime);
            operationLog.setExhibitionId(exhibitionId);
            operationLog.setCreateTime(LocalDateTime.now());

            save(operationLog);
            log.info("记录用户操作日志成功，用户: {}, 操作: {}, 模块: {}", username, operationType, operationModule);

            // 检查告警
            try {
                userLogAlertService.checkAndHandleAlert(operationLog);
            } catch (Exception alertException) {
                log.error("检查用户日志告警失败", alertException);
            }
        } catch (Exception e) {
            log.error("记录用户操作日志失败", e);
        }
    }

    @Override
    public void recordLoginLog(Integer userId, String username, String ipAddress, String userAgent, boolean success, String errorMessage) {
        String responseStatus = success ? UserOperationLog.STATUS_SUCCESS : UserOperationLog.STATUS_FAILED;
        String operationDetail = success ? "用户登录成功" : "用户登录失败: " + errorMessage;
        
        recordUserOperation(userId, username, UserOperationLog.OPERATION_LOGIN, UserOperationLog.MODULE_AUTH,
                          operationDetail, null, null, ipAddress, userAgent, "/auth/login", "POST", null,
                          responseStatus, errorMessage, null, null);
    }

    @Override
    public void recordLogoutLog(Integer userId, String username, String ipAddress, String userAgent) {
        recordUserOperation(userId, username, UserOperationLog.OPERATION_LOGOUT, UserOperationLog.MODULE_AUTH,
                          "用户登出", null, null, ipAddress, userAgent, "/auth/logout", "POST", null,
                          UserOperationLog.STATUS_SUCCESS, null, null, null);
    }

    @Override
    public List<UserOperationLogVO> getRecentUserLogs(Integer userId, Integer limit) {
        return userOperationLogMapper.selectRecentUserLogs(userId, limit);
    }

    @Override
    public List<Map<String, Object>> getUserOperationStatistics(Integer userId, LocalDateTime startTime, LocalDateTime endTime) {
        return userOperationLogMapper.selectUserOperationStatistics(userId, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getOperationTypeStatistics(Integer exhibitionId, LocalDateTime startTime, LocalDateTime endTime) {
        return userOperationLogMapper.selectOperationTypeStatistics(exhibitionId, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getOperationModuleStatistics(Integer exhibitionId, LocalDateTime startTime, LocalDateTime endTime) {
        return userOperationLogMapper.selectOperationModuleStatistics(exhibitionId, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getDailyOperationStatistics(Integer exhibitionId, LocalDateTime startTime, LocalDateTime endTime) {
        return userOperationLogMapper.selectDailyOperationStatistics(exhibitionId, startTime, endTime);
    }

    @Override
    public int cleanExpiredLogs(LocalDateTime beforeTime) {
        try {
            int totalDeleted = 0;
            int batchSize = 1000; // 批量删除大小
            int deletedCount;

            // 分批删除，避免长时间锁表
            do {
                deletedCount = userOperationLogMapper.deleteExpiredLogsBatch(beforeTime, batchSize);
                totalDeleted += deletedCount;

                if (deletedCount > 0) {
                    log.debug("批量删除过期日志 {} 条", deletedCount);
                    // 短暂休眠，减少数据库压力
                    Thread.sleep(100);
                }
            } while (deletedCount > 0);

            log.info("清理过期操作日志完成，总删除数量: {}", totalDeleted);
            return totalDeleted;
        } catch (Exception e) {
            log.error("清理过期操作日志失败", e);
            return 0;
        }
    }

    @Override
    public boolean batchRecordLogs(List<UserOperationLog> logs) {
        try {
            if (logs == null || logs.isEmpty()) {
                return true;
            }
            
            int insertedCount = userOperationLogMapper.batchInsert(logs);
            log.info("批量记录操作日志完成，插入数量: {}", insertedCount);
            return insertedCount > 0;
        } catch (Exception e) {
            log.error("批量记录操作日志失败", e);
            return false;
        }
    }
}
