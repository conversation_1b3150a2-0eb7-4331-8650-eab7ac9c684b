package zb.iot.service;

import zb.iot.entity.Device;

/**
 * 设备控制服务接口
 * 负责根据不同协议类型控制设备
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface IDeviceControlService {

    /**
     * 执行设备操作
     * @param deviceId 设备ID
     * @param action 操作类型 (power_on, power_off, restart)
     * @return 操作结果
     */
    DeviceControlResult executeDeviceAction(Integer deviceId, String action);

    /**
     * 批量执行设备操作
     * @param deviceIds 设备ID列表
     * @param action 操作类型
     * @return 批量操作结果
     */
    BatchDeviceControlResult batchExecuteDeviceAction(Integer[] deviceIds, String action);

    /**
     * 测试设备连接
     * @param deviceId 设备ID
     * @return 连接测试结果
     */
    boolean testDeviceConnection(Integer deviceId);

    /**
     * 设备控制结果
     */
    class DeviceControlResult {
        private boolean success;
        private String message;
        private Device device;
        private long executionTime;

        public DeviceControlResult(boolean success, String message, Device device, long executionTime) {
            this.success = success;
            this.message = message;
            this.device = device;
            this.executionTime = executionTime;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Device getDevice() { return device; }
        public void setDevice(Device device) { this.device = device; }
        public long getExecutionTime() { return executionTime; }
        public void setExecutionTime(long executionTime) { this.executionTime = executionTime; }
    }

    /**
     * 批量设备控制结果
     */
    class BatchDeviceControlResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private DeviceControlResult[] results;

        public BatchDeviceControlResult(int totalCount, int successCount, int failureCount, DeviceControlResult[] results) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.results = results;
        }

        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }
        public DeviceControlResult[] getResults() { return results; }
        public void setResults(DeviceControlResult[] results) { this.results = results; }
    }
}
