package zb.iot.controller.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 策略查询参数DTO
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
@Setter
public class StrategyQueryDTO {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 策略名称（模糊查询）
     */
    private String name;

    /**
     * 策略类型(1:群组,2:定时,3:联动)
     */
    private Integer type;

    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;

    /**
     * 展厅ID
     */
    private Integer exhibitionId;
}
