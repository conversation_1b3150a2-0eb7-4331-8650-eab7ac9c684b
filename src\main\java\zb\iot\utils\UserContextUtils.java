package zb.iot.utils;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import zb.iot.security.CustomUserDetails;
import zb.iot.security.JwtUtils;

/**
 * 用户上下文工具类
 * 用于获取当前登录用户的信息
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Component
public class UserContextUtils {

    private final JwtUtils jwtUtils;

    public UserContextUtils(JwtUtils jwtUtils) {
        this.jwtUtils = jwtUtils;
    }

    /**
     * 获取当前登录用户ID
     * @return 用户ID，如果未登录返回null
     */
    public static Integer getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
                CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
                return userDetails.getUserId();
            }
        } catch (Exception e) {
            log.debug("获取当前用户ID失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前登录用户名
     * @return 用户名，如果未登录返回"anonymous"
     */
    public static String getCurrentUsername() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
                CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
                return userDetails.getUsername();
            }
        } catch (Exception e) {
            log.debug("获取当前用户名失败: {}", e.getMessage());
        }
        return "anonymous";
    }

    /**
     * 获取当前登录用户详情
     * @return 用户详情，如果未登录返回null
     */
    public static CustomUserDetails getCurrentUserDetails() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
                return (CustomUserDetails) authentication.getPrincipal();
            }
        } catch (Exception e) {
            log.debug("获取当前用户详情失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前请求的IP地址
     * @return IP地址
     */
    public static String getCurrentUserIp() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return getClientIpAddress(request);
            }
        } catch (Exception e) {
            log.debug("获取当前用户IP失败: {}", e.getMessage());
        }
        return "unknown";
    }

    /**
     * 获取当前请求的User-Agent
     * @return User-Agent
     */
    public static String getCurrentUserAgent() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return request.getHeader("User-Agent");
            }
        } catch (Exception e) {
            log.debug("获取当前User-Agent失败: {}", e.getMessage());
        }
        return "unknown";
    }

    /**
     * 从JWT token中获取用户信息（备用方法）
     * @param request HTTP请求
     * @return 用户名，如果无法获取返回null
     */
    public String getUsernameFromToken(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7);
                return jwtUtils.extractUsername(token);
            }
        } catch (Exception e) {
            log.debug("从token获取用户名失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从JWT token中获取用户ID（备用方法）
     * @param request HTTP请求
     * @return 用户ID，如果无法获取返回null
     */
    public Integer getUserIdFromToken(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7);
                // 从token的claims中获取userId
                return jwtUtils.extractClaim(token, claims -> claims.get("userId", Integer.class));
            }
        } catch (Exception e) {
            log.debug("从token获取用户ID失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取客户端真实IP地址
     * @param request HTTP请求
     * @return IP地址
     */
    private static String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // 多级代理的情况，第一个IP为客户端真实IP
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        String xForwarded = request.getHeader("X-Forwarded");
        if (xForwarded != null && !xForwarded.isEmpty() && !"unknown".equalsIgnoreCase(xForwarded)) {
            return xForwarded;
        }

        String forwarded = request.getHeader("Forwarded");
        if (forwarded != null && !forwarded.isEmpty() && !"unknown".equalsIgnoreCase(forwarded)) {
            return forwarded;
        }

        return request.getRemoteAddr();
    }

    /**
     * 检查当前用户是否已登录
     * @return true表示已登录，false表示未登录
     */
    public static boolean isUserLoggedIn() {
        return getCurrentUserId() != null;
    }

    /**
     * 检查当前用户是否为管理员
     * @return true表示是管理员，false表示不是
     */
    public static boolean isCurrentUserAdmin() {
        try {
            CustomUserDetails userDetails = getCurrentUserDetails();
            if (userDetails != null) {
                // 检查用户是否有管理员权限
                return userDetails.getPermissions() != null && !userDetails.getPermissions().isEmpty();
            }
        } catch (Exception e) {
            log.debug("检查管理员权限失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 获取当前用户的展厅ID（从请求路径中提取）
     * @return 展厅ID，如果无法获取返回null
     */
    public static Integer getCurrentExhibitionId() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String uri = request.getRequestURI();
                
                // 从URL路径中提取展厅ID，例如 /api/exhibition/3/manage
                if (uri.contains("/exhibition/")) {
                    String[] parts = uri.split("/");
                    for (int i = 0; i < parts.length - 1; i++) {
                        if ("exhibition".equals(parts[i]) && i + 1 < parts.length) {
                            try {
                                return Integer.parseInt(parts[i + 1]);
                            } catch (NumberFormatException e) {
                                log.debug("解析展厅ID失败: {}", parts[i + 1]);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("获取当前展厅ID失败: {}", e.getMessage());
        }
        return null;
    }
}
