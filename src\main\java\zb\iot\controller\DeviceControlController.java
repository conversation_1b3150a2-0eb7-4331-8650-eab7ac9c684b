package zb.iot.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import zb.iot.common.ResponseResult;
import zb.iot.service.IDeviceControlService;

/**
 * 设备控制控制器
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@RestController
@RequestMapping("/device-control")
@RequiredArgsConstructor
public class DeviceControlController {

    private final IDeviceControlService deviceControlService;

    /**
     * 执行设备操作
     * @param deviceId 设备ID
     * @param action 操作类型
     * @return 操作结果
     */
    @PostMapping("/{deviceId}/execute")
    public ResponseResult<IDeviceControlService.DeviceControlResult> executeDeviceAction(
            @PathVariable Integer deviceId,
            @RequestParam String action) {
        try {
            IDeviceControlService.DeviceControlResult result = 
                deviceControlService.executeDeviceAction(deviceId, action);
            
            if (result.isSuccess()) {
                return ResponseResult.success(result);
            } else {
                return ResponseResult.fail(result.getMessage());
            }
        } catch (Exception e) {
            log.error("执行设备操作失败，设备ID: {}, 动作: {}", deviceId, action, e);
            return ResponseResult.fail("执行设备操作失败: " + e.getMessage());
        }
    }

    /**
     * 批量执行设备操作
     * @param deviceIds 设备ID数组
     * @param action 操作类型
     * @return 批量操作结果
     */
    @PostMapping("/batch-execute")
    public ResponseResult<IDeviceControlService.BatchDeviceControlResult> batchExecuteDeviceAction(
            @RequestBody Integer[] deviceIds,
            @RequestParam String action) {
        try {
            IDeviceControlService.BatchDeviceControlResult result = 
                deviceControlService.batchExecuteDeviceAction(deviceIds, action);
            
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("批量执行设备操作失败，设备数量: {}, 动作: {}", deviceIds.length, action, e);
            return ResponseResult.fail("批量执行设备操作失败: " + e.getMessage());
        }
    }

    /**
     * 测试设备连接
     * @param deviceId 设备ID
     * @return 连接测试结果
     */
    @PostMapping("/{deviceId}/test-connection")
    public ResponseResult<Boolean> testDeviceConnection(@PathVariable Integer deviceId) {
        try {
            boolean connected = deviceControlService.testDeviceConnection(deviceId);
            return ResponseResult.success(connected);
        } catch (Exception e) {
            log.error("测试设备连接失败，设备ID: {}", deviceId, e);
            return ResponseResult.fail("测试设备连接失败: " + e.getMessage());
        }
    }

    /**
     * 设备开机
     * @param deviceId 设备ID
     * @return 操作结果
     */
    @PostMapping("/{deviceId}/power-on")
    public ResponseResult<IDeviceControlService.DeviceControlResult> powerOnDevice(@PathVariable Integer deviceId) {
        return executeDeviceAction(deviceId, "power_on");
    }

    /**
     * 设备关机
     * @param deviceId 设备ID
     * @return 操作结果
     */
    @PostMapping("/{deviceId}/power-off")
    public ResponseResult<IDeviceControlService.DeviceControlResult> powerOffDevice(@PathVariable Integer deviceId) {
        return executeDeviceAction(deviceId, "power_off");
    }

    /**
     * 设备重启
     * @param deviceId 设备ID
     * @return 操作结果
     */
    @PostMapping("/{deviceId}/restart")
    public ResponseResult<IDeviceControlService.DeviceControlResult> restartDevice(@PathVariable Integer deviceId) {
        return executeDeviceAction(deviceId, "restart");
    }
}
