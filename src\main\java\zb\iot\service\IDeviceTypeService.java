package zb.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import zb.iot.entity.DeviceType;

import java.util.List;

/**
 * 设备类型表 服务类
 */
public interface IDeviceTypeService extends IService<DeviceType> {
    
    /**
     * 添加设备类型
     *
     * @param deviceType 设备类型信息
     * @return 是否成功
     */
    boolean addDeviceType(DeviceType deviceType);
    
    /**
     * 更新设备类型
     *
     * @param deviceType 设备类型信息
     * @return 是否成功
     */
    boolean updateDeviceType(DeviceType deviceType);
    
    /**
     * 删除设备类型
     *
     * @param id 设备类型ID
     * @return 是否成功
     */
    boolean deleteDeviceType(Integer id);
    
    /**
     * 获取设备类型详情
     *
     * @param id 设备类型ID
     * @return 设备类型详情
     */
    DeviceType getDeviceTypeById(Integer id);
    
    /**
     * 分页查询设备类型
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 设备类型名称(可选)
     * @param exhibitionId 展厅ID(可选)
     * @return 分页结果
     */
    IPage<DeviceType> pageDeviceTypes(Integer pageNum, Integer pageSize, String name, Integer exhibitionId);
    
    /**
     * 发布设备类型
     *
     * @param id 设备类型ID
     * @return 是否成功
     */
    boolean publishDeviceType(Integer id);
    
    /**
     * 取消发布设备类型
     *
     * @param id 设备类型ID
     * @return 是否成功
     */
    boolean unpublishDeviceType(Integer id);
    
    /**
     * 获取所有已发布的设备类型
     *
     * @param exhibitionId 展厅ID(可选)
     * @return 已发布的设备类型列表
     */
    List<DeviceType> getPublishedDeviceTypes(Integer exhibitionId);
}
