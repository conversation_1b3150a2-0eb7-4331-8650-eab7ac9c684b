<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zb.iot.mapper.DevicePolicyTagMapper">


    <!-- 批量插入设备策略标签关联 -->
    <insert id="batchInsert">
        INSERT INTO device_police_tag (device_id, policy_id, create_time, deleted)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.deviceId}, #{item.policyId}, #{item.createTime}, 0)
        </foreach>
    </insert>


</mapper>
