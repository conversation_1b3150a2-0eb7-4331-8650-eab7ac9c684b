package zb.iot.service;

import zb.iot.entity.Strategy;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定时任务服务接口
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface IScheduleTaskService {

    /**
     * 创建定时策略任务
     * @param strategy 策略对象
     * @return 任务是否创建成功
     */
    boolean createScheduleTask(Strategy strategy);

    /**
     * 取消定时策略任务
     * @param strategyId 策略ID
     * @return 任务是否取消成功
     */
    boolean cancelScheduleTask(Integer strategyId);

    /**
     * 更新定时策略任务
     * @param strategy 策略对象
     * @return 任务是否更新成功
     */
    boolean updateScheduleTask(Strategy strategy);

    /**
     * 获取所有活跃的定时任务
     * @return 活跃任务列表
     */
    List<ScheduleTaskInfo> getActiveTasks();

    /**
     * 手动触发定时策略执行
     * @param strategyId 策略ID
     * @return 执行是否成功
     */
    boolean triggerScheduleTask(Integer strategyId);

    /**
     * 检查策略是否有活跃的定时任务
     * @param strategyId 策略ID
     * @return 是否有活跃任务
     */
    boolean hasActiveTask(Integer strategyId);

    /**
     * 定时任务信息
     */
    class ScheduleTaskInfo {
        private Integer strategyId;
        private String strategyName;
        private String cronExpression;
        private LocalDateTime nextExecutionTime;
        private LocalDateTime lastExecutionTime;
        private String status;
        private String taskType;

        // 构造函数
        public ScheduleTaskInfo(Integer strategyId, String strategyName, String cronExpression, 
                               LocalDateTime nextExecutionTime, LocalDateTime lastExecutionTime, 
                               String status, String taskType) {
            this.strategyId = strategyId;
            this.strategyName = strategyName;
            this.cronExpression = cronExpression;
            this.nextExecutionTime = nextExecutionTime;
            this.lastExecutionTime = lastExecutionTime;
            this.status = status;
            this.taskType = taskType;
        }

        // Getters and Setters
        public Integer getStrategyId() { return strategyId; }
        public void setStrategyId(Integer strategyId) { this.strategyId = strategyId; }
        public String getStrategyName() { return strategyName; }
        public void setStrategyName(String strategyName) { this.strategyName = strategyName; }
        public String getCronExpression() { return cronExpression; }
        public void setCronExpression(String cronExpression) { this.cronExpression = cronExpression; }
        public LocalDateTime getNextExecutionTime() { return nextExecutionTime; }
        public void setNextExecutionTime(LocalDateTime nextExecutionTime) { this.nextExecutionTime = nextExecutionTime; }
        public LocalDateTime getLastExecutionTime() { return lastExecutionTime; }
        public void setLastExecutionTime(LocalDateTime lastExecutionTime) { this.lastExecutionTime = lastExecutionTime; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getTaskType() { return taskType; }
        public void setTaskType(String taskType) { this.taskType = taskType; }
    }
}
