package zb.iot.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import zb.iot.entity.Device;
import zb.iot.entity.Strategy;
import zb.iot.entity.StrategyExecutionLog;
import zb.iot.mapper.StrategyExecutionLogMapper;
import zb.iot.service.IDeviceControlService;
import zb.iot.service.IDeviceService;
import zb.iot.service.IStrategyExecutionService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 策略执行服务实现类
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StrategyExecutionServiceImpl implements IStrategyExecutionService {

    private final StrategyExecutionLogMapper executionLogMapper;
    private final IDeviceService deviceService;
    private final IDeviceControlService deviceControlService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 执行策略
     * @param strategy 策略对象
     * @param triggerType 触发类型
     * @return 策略执行日志
     */
    @Override
    public StrategyExecutionLog executeStrategy(Strategy strategy, String triggerType) {
        log.info("开始执行策略，策略ID: {}, 策略名称: {}, 类型: {}", 
                strategy.getId(), strategy.getName(), strategy.getType());

        StrategyExecutionLog executionLog = new StrategyExecutionLog();
        executionLog.setStrategyId(strategy.getId());
        executionLog.setExecutionTime(LocalDateTime.now());
        executionLog.setTriggerType(triggerType);

        long startTime = System.currentTimeMillis();

        try {
            // 根据策略类型执行不同的逻辑
            switch (strategy.getType()) {
                case Strategy.TYPE_GROUP:
                    executionLog = executeGroupStrategy(strategy, triggerType);
                    break;

                case Strategy.TYPE_SCHEDULE:
                    executionLog = executeScheduleStrategy(strategy, triggerType);
                    break;

                case Strategy.TYPE_LINKAGE:
                    executionLog = executeLinkageStrategy(strategy, triggerType);
                    break;
                default:
                    throw new RuntimeException("不支持的策略类型: " + strategy.getType());
            }

            executionLog.setStatus(StrategyExecutionLog.STATUS_SUCCESS);
            executionLog.setResult("策略执行成功");

        } catch (Exception e) {
            log.error("策略执行失败，策略ID: {}", strategy.getId(), e);
            executionLog.setStatus(StrategyExecutionLog.STATUS_FAILED);
            executionLog.setErrorMessage(e.getMessage());
            executionLog.setResult("策略执行失败: " + e.getMessage());
        } finally {
            long endTime = System.currentTimeMillis();
            executionLog.setExecutionDuration((int) (endTime - startTime));
            
            // 保存执行记录
            executionLogMapper.insert(executionLog);
        }

        return executionLog;
    }

    /**
     * 验证策略配置
     * @param type 策略类型
     * @param config 策略配置（JSON格式字符串）
     * @return 配置是否有效
     */
    @Override
    public boolean validateStrategyConfig(Integer type, String config) {
        try {
            JsonNode configNode = objectMapper.readTree(config);
            
            switch (type) {
                case Strategy.TYPE_GROUP:
                    return validateGroupConfig(configNode);
                case Strategy.TYPE_SCHEDULE:
                    return validateScheduleConfig(configNode);
                case Strategy.TYPE_LINKAGE:
                    return validateLinkageConfig(configNode);
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("验证策略配置失败", e);
            return false;
        }
    }

    /**
     * 执行群组策略
     * @param strategy 策略对象
     * @param triggerType 触发类型
     * @return 策略执行日志
     */
    @Override
    public StrategyExecutionLog executeGroupStrategy(Strategy strategy, String triggerType) {
        log.info("执行群组策略，策略ID: {}", strategy.getId());

        StrategyExecutionLog executionLog = new StrategyExecutionLog();
        executionLog.setStrategyId(strategy.getId());
        executionLog.setExecutionTime(LocalDateTime.now());
        executionLog.setTriggerType(triggerType);
        Byte status =  null;

        try {
            JsonNode config = objectMapper.readTree(strategy.getConfig());
            JsonNode devicesNode = config.get("devices");
            String defaultAction = config.get("defaultAction").asText();
            switch (defaultAction) {
                case "power_on":
                    status = 1;
                    break;
                case "power_off":
                    status = 0;
                    break;
                default:
                    log.warn("未知动作: {}", defaultAction);
                    break;
            }

            List<String> affectedDevices = new ArrayList<>();
            StringBuilder resultBuilder = new StringBuilder();

            if (devicesNode != null && devicesNode.isArray()) {
                for (JsonNode deviceNode : devicesNode) {
                    int deviceId = deviceNode.get("id").asInt();
                    String deviceName = deviceNode.get("name").asText();
                    String ipAddress = deviceNode.get("ipAddress").asText();

                    try {
                        // 执行设备操作
                        boolean success = executeDeviceAction(deviceId, ipAddress, defaultAction);
                        
                        if (success) {
                            affectedDevices.add(deviceName);
                            deviceService.updateDeviceByStatus(deviceId, status);
                            resultBuilder.append(String.format("设备 %s 操作成功; ", deviceName));
                        } else {
                            resultBuilder.append(String.format("设备 %s 操作失败; ", deviceName));
                        }
                    } catch (Exception e) {
                        log.error("设备操作失败，设备ID: {}", deviceId, e);
                        resultBuilder.append(String.format("设备 %s 操作异常: %s; ", deviceName, e.getMessage()));
                    }
                }
            }

            executionLog.setAffectedDevices(objectMapper.writeValueAsString(affectedDevices));
            executionLog.setResult(resultBuilder.toString());

        } catch (Exception e) {
            throw new RuntimeException("群组策略执行失败: " + e.getMessage(), e);
        }

        return executionLog;
    }

    /**
     * 执行定时策略
     * @param strategy 策略对象
     * @param triggerType 触发类型
     * @return 策略执行日志
     */
    @Override
    public StrategyExecutionLog executeScheduleStrategy(Strategy strategy, String triggerType) {
        log.info("执行定时策略，策略ID: {}", strategy.getId());

        StrategyExecutionLog executionLog = new StrategyExecutionLog();
        executionLog.setStrategyId(strategy.getId());
        executionLog.setExecutionTime(LocalDateTime.now());
        executionLog.setTriggerType(triggerType);

        try {
            JsonNode config = objectMapper.readTree(strategy.getConfig());
            JsonNode targetDevicesNode = config.get("targetDevices");
            String action = config.get("action").asText();

            List<String> affectedDevices = new ArrayList<>();
            StringBuilder resultBuilder = new StringBuilder();

            if (targetDevicesNode != null && targetDevicesNode.isArray()) {
                for (JsonNode deviceNode : targetDevicesNode) {
                    int deviceId = deviceNode.get("id").asInt();
                    String deviceName = deviceNode.get("name").asText();
                    String ipAddress = deviceNode.get("ipAddress").asText();

                    try {
                        boolean success = executeDeviceAction(deviceId, ipAddress, action);
                        
                        if (success) {
                            affectedDevices.add(deviceName);
                            resultBuilder.append(String.format("设备 %s 定时操作成功; ", deviceName));
                        } else {
                            resultBuilder.append(String.format("设备 %s 定时操作失败; ", deviceName));
                        }
                    } catch (Exception e) {
                        log.error("定时设备操作失败，设备ID: {}", deviceId, e);
                        resultBuilder.append(String.format("设备 %s 定时操作异常: %s; ", deviceName, e.getMessage()));
                    }
                }
            }

            executionLog.setAffectedDevices(objectMapper.writeValueAsString(affectedDevices));
            executionLog.setResult(resultBuilder.toString());

        } catch (Exception e) {
            throw new RuntimeException("定时策略执行失败: " + e.getMessage(), e);
        }

        return executionLog;
    }

    /**
     * 执行联动策略
     * @param strategy 策略对象
     * @param triggerType 触发类型
     * @return 策略执行日志
     */
    @Override
    public StrategyExecutionLog executeLinkageStrategy(Strategy strategy, String triggerType) {
        log.info("执行联动策略，策略ID: {}", strategy.getId());

        StrategyExecutionLog executionLog = new StrategyExecutionLog();
        executionLog.setStrategyId(strategy.getId());
        executionLog.setExecutionTime(LocalDateTime.now());
        executionLog.setTriggerType(triggerType);

        try {
            JsonNode config = objectMapper.readTree(strategy.getConfig());
            JsonNode actionsNode = config.get("actions");

            List<String> affectedDevices = new ArrayList<>();
            StringBuilder resultBuilder = new StringBuilder();
            Byte status = 0;
            if (actionsNode != null && actionsNode.isArray()) {
                for (JsonNode actionNode : actionsNode) {
                    int deviceId = actionNode.get("deviceId").asInt();
                    String actionType = actionNode.get("actionType").asText();

                    // switch (actionType) {
                    //     case "power_on":
                    //         status = 1;
                    //         break;
                    //     case "power_off":
                    //         status = 0;
                    //         break;
                    //     default:
                    //         log.warn("未知动作: {}", actionType);
                    //         break;
                    // }

                    try {
                        // 获取设备信息
                        Device device = deviceService.getById(deviceId);
                        if (device != null) {
                            boolean success = executeDeviceAction(deviceId, device.getIpAddress(), actionType);
                            
                            if (success) {
                                affectedDevices.add(device.getName());
                                resultBuilder.append(String.format("设备 %s 联动操作成功; ", device.getName()));
                            } else {
                                resultBuilder.append(String.format("设备 %s 联动操作失败; ", device.getName()));
                            }
                        }
                    } catch (Exception e) {
                        log.error("联动设备操作失败，设备ID: {}", deviceId, e);
                        resultBuilder.append(String.format("设备ID %d 联动操作异常: %s; ", deviceId, e.getMessage()));
                    }
                }
            }

            executionLog.setAffectedDevices(objectMapper.writeValueAsString(affectedDevices));
            executionLog.setResult(resultBuilder.toString());

        } catch (Exception e) {
            throw new RuntimeException("联动策略执行失败: " + e.getMessage(), e);
        }

        return executionLog;
    }

    /**
     * 执行设备操作
     * @param deviceId 设备ID
     * @param ipAddress 设备IP地址（保留参数兼容性）
     * @param action 操作类型
     * @return 操作是否成功
     */
    private boolean executeDeviceAction(int deviceId, String ipAddress, String action) {
        try {
            IDeviceControlService.DeviceControlResult result =
                deviceControlService.executeDeviceAction(deviceId, action);

            if (result.isSuccess()) {
                log.info("设备操作成功，设备ID: {}, 动作: {}, 耗时: {}ms",
                    deviceId, action, result.getExecutionTime());
            } else {
                log.error("设备操作失败，设备ID: {}, 动作: {}, 原因: {}",
                    deviceId, action, result.getMessage());
            }

            return result.isSuccess();
        } catch (Exception e) {
            log.error("设备操作异常，设备ID: {}, 动作: {}", deviceId, action, e);
            return false;
        }
    }

    /**
     * 验证群组策略配置
     * @param config 策略配置JSON节点
     * @return 配置是否有效
     */
    private boolean validateGroupConfig(JsonNode config) {
        return config.has("devices") && config.has("defaultAction");
    }

    /**
     * 验证定时策略配置
     * @param config 策略配置JSON节点
     * @return 配置是否有效
     */
    private boolean validateScheduleConfig(JsonNode config) {
        return config.has("scheduleType") && config.has("targetDevices") && config.has("action");
    }

    /**
     * 验证联动策略配置
     * @param config 策略配置JSON节点
     * @return 配置是否有效
     */
    private boolean validateLinkageConfig(JsonNode config) {
        return config.has("triggerDeviceId") && config.has("actions");
    }
}
