package zb.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import zb.iot.controller.dto.AdminInfoDTO;
import zb.iot.entity.AdminPermission;
import zb.iot.entity.Administrator;
import zb.iot.mapper.AdminPermissionMapper;
import zb.iot.mapper.AdministratorMapper;
import zb.iot.mapper.PermissionMapper;
import zb.iot.service.IAdministratorService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import zb.iot.utils.OssStorageUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 管理员表 服务实现类
 * <AUTHOR>
 * @since 2025-03-21
 */
@Service
@RequiredArgsConstructor
public class AdministratorServiceImpl extends ServiceImpl<AdministratorMapper, Administrator> implements IAdministratorService {

    private final AdminPermissionMapper adminPermissionMapper;

    private final OssStorageUtil ossStorageUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Administrator addAdminWithPermissions(Integer exhibitionId, Integer userId, List<Integer> permissionIds) {

        // 1. 检查用户是否已经是该展厅的管理员
        Administrator existingAdmin = getAdministratorByExhibitionAndUser(exhibitionId, userId);
        if (existingAdmin != null) {
            throw new RuntimeException("该用户已经是此展厅的管理员");
        }

        // 2. 创建管理员记录
        Administrator admin = new Administrator();
        admin.setExhibitionId(exhibitionId);
        admin.setUserId(userId);

        // 3. 保存管理员记录
        this.save(admin);

        // 4. 如果有权限需要分配，建立管理员与权限的关联
        if (permissionIds != null && !permissionIds.isEmpty()) {
            assignPermissions(admin.getId(), permissionIds);
        }

        // 5. 加载关联的权限信息
        return getAdministratorWithPermissions(admin.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Administrator updateAdministratorPermissions(Integer adminId, List<Integer> permissionIds) {
        // 1. 检查管理员是否存在
        Administrator admin = this.getById(adminId);
        if (admin == null) {
            throw new RuntimeException("管理员不存在");
        }

        // 2. 删除现有权限关联
        adminPermissionMapper.deleteByAdminId(adminId);

        // 3. 如果有新权限，创建新的关联
        if (permissionIds != null && !permissionIds.isEmpty()) {
            assignPermissions(adminId, permissionIds);
        }

        // 4. 更新管理员
        this.updateById(admin);

        // 5. 加载更新后的权限信息
        return getAdministratorWithPermissions(adminId);
    }

    @Override
    public Administrator getAdministratorWithPermissions(Integer adminId) {
        // 使用联表查询获取管理员及其权限
        return baseMapper.getAdminWithPermissions(adminId);
    }

    @Override
    public Administrator getAdministratorByExhibitionAndUser(Integer exhibitionId, Integer userId) {
        LambdaQueryWrapper<Administrator> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Administrator::getExhibitionId, exhibitionId)
                .eq(Administrator::getUserId, userId);

        return this.getOne(queryWrapper, false);
    }

    @Override
    public List<AdminInfoDTO> getAdminsByExhibitionId(Integer exhibitionId) {
        List<AdminInfoDTO> admins = baseMapper.getAdminsByExhibitionId(exhibitionId);

        // 处理头像URL
        for (AdminInfoDTO admin : admins) {
            processAvatarUrl(admin);
        }

        return admins;
    }

    @Override
    public List<AdminInfoDTO> getNonAdminUsersByExhibitionId(Integer exhibitionId) {
        // 查询非管理员用户信息
        List<AdminInfoDTO> nonAdminUsers = baseMapper.getNonAdminUsersByExhibitionId(exhibitionId);

        // 处理头像URL
        for (AdminInfoDTO user : nonAdminUsers) {
            processAvatarUrl(user);
        }

        return nonAdminUsers;
    }

    /**
     * 为管理员分配权限
     */
    private void assignPermissions(Integer adminId, List<Integer> permissionIds) {
        List<AdminPermission> adminPermissions = new ArrayList<>();

        for (Integer permissionId : permissionIds) {
            AdminPermission adminPermission = new AdminPermission();
            adminPermission.setAdminId(adminId);
            adminPermission.setPermissionId(permissionId);
            adminPermission.setCreateTime(LocalDateTime.now());
            adminPermissions.add(adminPermission);
        }

        // 批量插入权限关联
        if (!adminPermissions.isEmpty()) {
            adminPermissionMapper.batchInsert(adminPermissions);
        }
    }

    /**
     * 处理头像URL，转换为可访问的OSS URL
     */
    private void processAvatarUrl(AdminInfoDTO admin) {
        if (admin.getAvatarUrl() != null && !admin.getAvatarUrl().isEmpty()) {
            // 转换为可访问的URL
            admin.setAvatarUrl(ossStorageUtil.getPublicUrl(admin.getAvatarUrl()));
        } else {
            // 设置默认头像
            admin.setAvatarUrl("http://localhost:8080/api/User.png");
        }
    }
}
