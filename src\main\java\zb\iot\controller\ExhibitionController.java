package zb.iot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import zb.iot.annotation.UserLog;
import zb.iot.common.ResponseResult;
import zb.iot.controller.dto.ExhibitionDTO;
import zb.iot.entity.Exhibition;
import zb.iot.service.IExhibitionService;
import zb.iot.utils.OssStorageUtil;

import java.io.IOException;

/**
 * 展厅信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@RestController
@RequestMapping("/exhibition")
@RequiredArgsConstructor
@Slf4j
public class ExhibitionController {

    private final IExhibitionService exhibitionService;

    /**
     * 分页查询展厅列表
     *
     * @param page 当前页码，默认为1
     * @param pageSize 每页记录数，默认为8
     * @param exhibitionName 展厅名称，可选参数，用于模糊查询
     * @return 分页结果
     */
    @PostMapping("/getExhibition")
    public ResponseResult<IPage<ExhibitionDTO>> getExhibitionPage(@RequestParam(defaultValue = "1") Integer page,
                                                                  @RequestParam(defaultValue = "8") Integer pageSize,
                                                                  @RequestParam(required = false) String exhibitionName){

        IPage<ExhibitionDTO> exhibitionPage = exhibitionService.getExhibitionPage(page, pageSize, exhibitionName);

        return ResponseResult.success(exhibitionPage);
    }

    /**
     * 添加展厅
     * @param logo 图片
     * @return 分页结果
     */
    @PostMapping("/addExhibition")
    @UserLog(operationType = "CREATE", operationModule = "EXHIBITION", description = "添加展厅")
    public ResponseResult<String> addExhibition(@RequestParam String name,
                                                @RequestParam String description,
                                                @RequestParam String address,
                                                @RequestParam String exhibitionIP,
                                                @RequestParam("logo") MultipartFile logo) {
        ExhibitionDTO exhibitionDTO = new ExhibitionDTO();
        exhibitionDTO.setName(name);
        exhibitionDTO.setDescription(description);
        exhibitionDTO.setAddress(address);
        exhibitionDTO.setExhibitionIP(exhibitionIP);


        if (exhibitionService.addExhibition(exhibitionDTO, logo)){
            return ResponseResult.success();
        }
        return ResponseResult.fail();

    }

    /**
     * 更新展厅信息
     *
     * @param exhibition 展厅信息对象
     * @return 更新结果
     */
    @PutMapping("/updateExhibition")
    @UserLog(operationType = "UPDATE", operationModule = "EXHIBITION", description = "更新展厅信息")
    public ResponseResult<String> updateExhibition(@RequestBody Exhibition exhibition) {

        if (exhibitionService.updateExhibition(exhibition)) {
            return ResponseResult.success();
        }
        return ResponseResult.fail();

    }

    /**
     * 根据ID删除展厅
     *
     * @param id 展厅ID
     * @return 删除结果
     */
    @DeleteMapping("/removeExhibition/{id}")
    @UserLog(operationType = "DELETE", operationModule = "EXHIBITION", description = "删除展厅")
    public ResponseResult<String> removeExhibitionById(@PathVariable Integer id) {

        if (exhibitionService.removeExhibitionById(id)) {
            return ResponseResult.success();
        }
        return ResponseResult.fail();

    }

}
