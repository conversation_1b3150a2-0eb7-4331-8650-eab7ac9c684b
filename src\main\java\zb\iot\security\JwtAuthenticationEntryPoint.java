package zb.iot.security;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        // 准备错误消息
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("code", 401);
        errorData.put("message", "认证失败: " + authException.getMessage());
        errorData.put("success", false);
        
        // 将错误信息写入响应
        response.getWriter().write(JSON.toJSONString(errorData));
    }
} 