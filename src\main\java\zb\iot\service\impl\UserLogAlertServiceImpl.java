package zb.iot.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import zb.iot.config.UserLogAlertConfig;
import zb.iot.entity.UserLogAlert;
import zb.iot.entity.UserOperationLog;
import zb.iot.mapper.UserOperationLogMapper;
import zb.iot.service.IUserLogAlertService;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 用户日志告警服务实现类
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "app.user-log.alert", name = "enabled", havingValue = "true", matchIfMissing = true)
public class UserLogAlertServiceImpl implements IUserLogAlertService {

    private final UserLogAlertConfig alertConfig;
    private final UserOperationLogMapper userOperationLogMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    private final JavaMailSender mailSender;

    private static final String ALERT_COOLDOWN_PREFIX = "user_log_alert_cooldown:";
    private static final String LOGIN_FAILURE_COUNT_PREFIX = "login_failure_count:";
    private static final String IP_OPERATION_COUNT_PREFIX = "ip_operation_count:";
    private static final String ERROR_OPERATION_COUNT_PREFIX = "error_operation_count:";

    @Override
    @Async
    public void checkAndHandleAlert(UserOperationLog userlog) {
        if (!alertConfig.getEnabled()) {
            return;
        }

        try {
            // 检查登录失败告警
            if (UserOperationLog.OPERATION_LOGIN.equals(userlog.getOperationType())
                && UserOperationLog.STATUS_FAILED.equals(userlog.getResponseStatus())) {
                checkLoginFailureAlert(userlog.getUsername(), userlog.getIpAddress());
            }

            // 检查可疑IP告警
            if (userlog.getIpAddress() != null) {
                checkSuspiciousIpAlert(userlog.getIpAddress());
            }

            // 检查错误操作告警
            if (UserOperationLog.STATUS_ERROR.equals(userlog.getResponseStatus())) {
                checkErrorOperationAlert(userlog.getUserId(), userlog.getUsername(), userlog.getIpAddress());
            }

        } catch (Exception e) {
            log.error("检查日志告警失败", e);
        }
    }

    @Override
    public void checkLoginFailureAlert(String username, String ipAddress) {
        String key = LOGIN_FAILURE_COUNT_PREFIX + username + ":" + ipAddress;
        String alertKey = "LOGIN_FAILURE:" + username + ":" + ipAddress;

        // 检查冷却时间
        if (isInCooldown(UserLogAlert.ALERT_TYPE_LOGIN_FAILURE, alertKey)) {
            return;
        }

        // 增加失败计数
        Long count = redisTemplate.opsForValue().increment(key);
        if (count == 1) {
            // 设置过期时间
            redisTemplate.expire(key, alertConfig.getTimeWindowMinutes(), TimeUnit.MINUTES);
        }

        // 检查是否达到阈值
        if (count >= alertConfig.getLoginFailureThreshold()) {
            String title = "登录失败告警";
            String content = String.format("用户 %s 从IP %s 在 %d 分钟内登录失败 %d 次，已达到告警阈值 %d 次",
                    username, ipAddress, alertConfig.getTimeWindowMinutes(), count, alertConfig.getLoginFailureThreshold());

            sendAlert(UserLogAlert.ALERT_TYPE_LOGIN_FAILURE, UserLogAlert.ALERT_LEVEL_HIGH,
                    title, content, null, username, ipAddress);

            // 设置冷却时间
            setCooldown(UserLogAlert.ALERT_TYPE_LOGIN_FAILURE, alertKey);
        }
    }

    @Override
    public void checkSuspiciousIpAlert(String ipAddress) {
        String key = IP_OPERATION_COUNT_PREFIX + ipAddress;
        String alertKey = "SUSPICIOUS_IP:" + ipAddress;

        // 检查冷却时间
        if (isInCooldown(UserLogAlert.ALERT_TYPE_SUSPICIOUS_IP, alertKey)) {
            return;
        }

        // 增加操作计数
        Long count = redisTemplate.opsForValue().increment(key);
        if (count == 1) {
            // 设置过期时间
            redisTemplate.expire(key, alertConfig.getTimeWindowMinutes(), TimeUnit.MINUTES);
        }

        // 检查是否达到阈值
        if (count >= alertConfig.getSuspiciousIpThreshold()) {
            String title = "可疑IP告警";
            String content = String.format("IP地址 %s 在 %d 分钟内执行了 %d 次操作，已达到告警阈值 %d 次，可能存在异常行为",
                    ipAddress, alertConfig.getTimeWindowMinutes(), count, alertConfig.getSuspiciousIpThreshold());

            sendAlert(UserLogAlert.ALERT_TYPE_SUSPICIOUS_IP, UserLogAlert.ALERT_LEVEL_MEDIUM,
                    title, content, null, null, ipAddress);

            // 设置冷却时间
            setCooldown(UserLogAlert.ALERT_TYPE_SUSPICIOUS_IP, alertKey);
        }
    }

    @Override
    public void checkErrorOperationAlert(Integer userId, String username, String ipAddress) {
        String key = ERROR_OPERATION_COUNT_PREFIX + userId;
        String alertKey = "ERROR_OPERATION:" + userId;

        // 检查冷却时间
        if (isInCooldown(UserLogAlert.ALERT_TYPE_ERROR_OPERATION, alertKey)) {
            return;
        }

        // 增加错误计数
        Long count = redisTemplate.opsForValue().increment(key);
        if (count == 1) {
            // 设置过期时间
            redisTemplate.expire(key, alertConfig.getTimeWindowMinutes(), TimeUnit.MINUTES);
        }

        // 检查是否达到阈值
        if (count >= alertConfig.getErrorOperationThreshold()) {
            String title = "错误操作告警";
            String content = String.format("用户 %s (ID: %d) 从IP %s 在 %d 分钟内发生 %d 次错误操作，已达到告警阈值 %d 次",
                    username, userId, ipAddress, alertConfig.getTimeWindowMinutes(), count, alertConfig.getErrorOperationThreshold());

            sendAlert(UserLogAlert.ALERT_TYPE_ERROR_OPERATION, UserLogAlert.ALERT_LEVEL_MEDIUM,
                    title, content, userId, username, ipAddress);

            // 设置冷却时间
            setCooldown(UserLogAlert.ALERT_TYPE_ERROR_OPERATION, alertKey);
        }
    }

    @Override
    @Async
    public void sendAlert(String alertType, String alertLevel, String title, String content,
                         Integer userId, String username, String ipAddress) {
        try {
            // 记录告警到数据库（如果有告警表的话）
            log.warn("用户日志告警: [{}] {} - {}", alertLevel, title, content);

            // 发送邮件告警
            if (alertConfig.getAlertEmails() != null && !alertConfig.getAlertEmails().isEmpty()) {
                sendEmailAlert(title, content);
            }

            // 这里可以添加其他告警方式，如：
            // - 发送短信
            // - 推送到钉钉/企业微信
            // - 调用第三方告警系统API
            // - 发送系统通知

        } catch (Exception e) {
            log.error("发送告警失败: {}", title, e);
        }
    }

    @Override
    public boolean isInCooldown(String alertType, String key) {
        String cooldownKey = ALERT_COOLDOWN_PREFIX + alertType + ":" + key;
        return Boolean.TRUE.equals(redisTemplate.hasKey(cooldownKey));
    }

    /**
     * 设置告警冷却时间
     */
    private void setCooldown(String alertType, String key) {
        String cooldownKey = ALERT_COOLDOWN_PREFIX + alertType + ":" + key;
        redisTemplate.opsForValue().set(cooldownKey, "1", alertConfig.getCooldownMinutes(), TimeUnit.MINUTES);
    }

    /**
     * 发送邮件告警
     */
    private void sendEmailAlert(String title, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(alertConfig.getAlertEmails().toArray(new String[0]));
            message.setSubject("[系统告警] " + title);
            message.setText(content + "\n\n告警时间: " + LocalDateTime.now());

            mailSender.send(message);
            log.info("告警邮件发送成功: {}", title);

        } catch (Exception e) {
            log.error("发送告警邮件失败: {}", title, e);
        }
    }
}
