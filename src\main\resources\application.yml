server:
  port: 8080
  servlet:
    context-path: /api

logging:
  level:
    root: info

spring:
  application:
    name: exhibition-system
  # ?????
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************
    username: root
    password: 12345678
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      maximum-pool-size: 15
      minimum-idle: 5
      idle-timeout: 30000
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: hfanriozfttubfif
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

  # Redis??
  data:
    redis:
      host: *************
      port: 6379
      database: 0
      timeout: 10000ms
      password: redis_PBZCTX

  # RabbitMQ??
#  rabbitmq:
#    host: localhost
#    port: 5672
#    username: guest
#    password: guest
#    virtual-host: /
#    listener:
#      simple:
#        acknowledge-mode: manual
#        prefetch: 1

  # ??????
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      enabled: true
  web:
    resources:
      static-locations: classpath:/static,file:${file.upload.dir}
file:
  upload:
    dir: D:/zb/center_control/upload/

# UDP通信配置
udp:
  # 默认UDP监听端口
  port: 9000
  # 默认缓冲区大小(字节)
  buffer-size: 1024
  # 默认超时时间(毫秒)
  timeout: 3000
  # 是否自动启动UDP监听器
  auto-start-listener: false
  # 多播组地址
  multicast-address: *********
  # 多播TTL值
  multicast-ttl: 1

# MyBatis-Plus??
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  type-aliases-package: zb.iot.entity

  configuration:
    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

aliyun:
  oss:
    endpoint: https://oss-cn-shenzhen.aliyuncs.com  # OSS端点，根据您的区域调整
    accessKeyId: LTAI5tEvweJs3As7cCbpYdeX                # 您的AccessKey ID
    accessKeySecret: ******************************         # 您的AccessKey Secret
    bucketName: jetson999                    # 您的Bucket名称
    urlPrefix: https://jetson999.oss-cn-shenzhen.aliyuncs.com/ # 文件访问前缀

# MQTT??
#mqtt:
#  broker: tcp://localhost:1883
#  client-id: exhibition_system_${random.int}
#  username: admin
#  password: 123456
#  default-topic: exhibition/device/+/status
#  timeout: 30



  # JWT??
app:
  jwt:
    secret: VqZ7h8cW4gT9yN2xP5kR3mA6bE1jL0sD7fH2pU3vX8zC4kB9gM5nF6tR
    expiration-ms: 86400000  # 24??

  # 用户日志配置
  user-log:
    # 日志保留天数
    retention-days: 90
    # 定时清理任务cron表达式（每天凌晨2点执行）
    cleanup-cron: "0 0 2 * * ?"
    # 是否启用定时清理
    cleanup-enabled: true
    # 批量删除大小
    batch-size: 1000

    # 告警配置
    alert:
      # 是否启用告警
      enabled: true
      # 登录失败次数阈值（时间窗口内）
      login-failure-threshold: 5
      # 时间窗口（分钟）
      time-window-minutes: 15
      # 异常IP检测阈值（时间窗口内的操作次数）
      suspicious-ip-threshold: 100
      # 错误操作阈值
      error-operation-threshold: 10
      # 告警邮件接收者
      alert-emails:
        - <EMAIL>
      # 告警冷却时间（分钟）
      cooldown-minutes: 60





# 微信配置
#wechat:
#  appid: wx123456789abcdef
#  secret: abcdef123456789abcdef123456789ab
#  redirect-uri: http://localhost:8080/api/auth/wechat/callback