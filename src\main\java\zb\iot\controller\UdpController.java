package zb.iot.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import zb.iot.annotation.UserLog;
import zb.iot.common.ResponseResult;
import zb.iot.config.UdpConfig;
import zb.iot.service.IUdpService;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * UDP控制器
 * 提供UDP通信相关的REST接口
 */
@Slf4j
@RestController
@RequestMapping("/udp")
@RequiredArgsConstructor
public class UdpController {

    private final IUdpService udpService;
    private final UdpConfig udpConfig;

    /**
     * 发送UDP消息
     * @param host 目标主机
     * @param port 目标端口
     * @param message 消息内容
     * @return 发送结果
     */
    @PostMapping("/send")
    @UserLog(operationType = "EXECUTE", operationModule = "DEVICE", description = "发送UDP消息")
    public ResponseResult<String> sendUdpByHexMessage(
            @RequestParam String host,
            @RequestParam(defaultValue = "9000") Integer port,
            @RequestParam String message) {
        try {
            boolean result = udpService.sendHexMessage(host, port, message);
            if (result) {
                return ResponseResult.success("UDP消息发送成功");
            } else {
                return ResponseResult.fail("UDP消息发送失败");
            }
        } catch (Exception e) {
            log.error("发送UDP消息异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
    
    /**
     * 发送指定编码的UDP消息
     *
     * @param host 目标主机
     * @param port 目标端口
     * @param message 消息内容
     * @param charsetName 字符编码名称
     * @return 发送结果
     */
    @PostMapping("/send/charset")
    public ResponseResult<String> sendUdpMessageWithCharset(
            @RequestParam String host,
            @RequestParam(defaultValue = "9000") Integer port,
            @RequestParam String message,
            @RequestParam String charsetName) {
        try {
            Charset charset = Charset.forName(charsetName);
            boolean result = udpService.sendMessage(host, port, message, charset);
            if (result) {
                return ResponseResult.success("指定编码(" + charsetName + ")的UDP消息发送成功");
            } else {
                return ResponseResult.fail("指定编码(" + charsetName + ")的UDP消息发送失败");
            }
        } catch (Exception e) {
            log.error("发送指定编码UDP消息异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
    
    /**
     * 发送16进制格式的UDP消息
     *
     * @param host 目标主机
     * @param port 目标端口
     * @param hexString 16进制字符串
     * @return 发送结果
     */
    @PostMapping("/send/hex")
    public ResponseResult<String> sendHexUdpMessage(
            @RequestParam String host,
            @RequestParam(defaultValue = "9000") Integer port,
            @RequestParam String hexString) {
        try {
            boolean result = udpService.sendHexMessage(host, port, hexString);
            if (result) {
                return ResponseResult.success("16进制UDP消息发送成功");
            } else {
                return ResponseResult.fail("16进制UDP消息发送失败");
            }
        } catch (Exception e) {
            log.error("发送16进制UDP消息异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 广播UDP消息
     *
     * @param port 广播端口
     * @param message 消息内容
     * @return 广播结果
     */
    @PostMapping("/broadcast")
    public ResponseResult<String> broadcastUdpMessage(
            @RequestParam(defaultValue = "9000") Integer port,
            @RequestParam String message) {
        try {
            boolean result = udpService.broadcastMessage(port, message);
            if (result) {
                return ResponseResult.success("UDP广播消息发送成功");
            } else {
                return ResponseResult.fail("UDP广播消息发送失败");
            }
        } catch (Exception e) {
            log.error("广播UDP消息异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
    
    /**
     * 广播指定编码的UDP消息
     *
     * @param port 广播端口
     * @param message 消息内容
     * @param charsetName 字符编码名称
     * @return 广播结果
     */
    @PostMapping("/broadcast/charset")
    public ResponseResult<String> broadcastUdpMessageWithCharset(
            @RequestParam(defaultValue = "9000") Integer port,
            @RequestParam String message,
            @RequestParam String charsetName) {
        try {
            Charset charset = Charset.forName(charsetName);
            boolean result = udpService.broadcastMessage(port, message, charset);
            if (result) {
                return ResponseResult.success("指定编码(" + charsetName + ")的UDP广播消息发送成功");
            } else {
                return ResponseResult.fail("指定编码(" + charsetName + ")的UDP广播消息发送失败");
            }
        } catch (Exception e) {
            log.error("广播指定编码UDP消息异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
    
    /**
     * 广播16进制格式的UDP消息
     *
     * @param port 广播端口
     * @param hexString 16进制字符串
     * @return 广播结果
     */
    @PostMapping("/broadcast/hex")
    public ResponseResult<String> broadcastHexUdpMessage(
            @RequestParam(defaultValue = "9000") Integer port,
            @RequestParam String hexString) {
        try {
            boolean result = udpService.broadcastHexMessage(port, hexString);
            if (result) {
                return ResponseResult.success("16进制UDP广播消息发送成功");
            } else {
                return ResponseResult.fail("16进制UDP广播消息发送失败");
            }
        } catch (Exception e) {
            log.error("广播16进制UDP消息异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
    
    /**
     * 发送设备控制命令
     *
     * @param deviceIp 设备IP地址
     * @param port 设备控制端口
     * @param command 控制命令
     * @return 发送结果
     */
    @PostMapping("/device/command")
    public ResponseResult<String> sendDeviceCommand(
            @RequestParam String deviceIp,
            @RequestParam(defaultValue = "9000") Integer port,
            @RequestParam String command) {
        try {
            boolean result = udpService.sendDeviceCommand(deviceIp, port, command);
            if (result) {
                return ResponseResult.success("设备控制命令发送成功");
            } else {
                return ResponseResult.fail("设备控制命令发送失败");
            }
        } catch (Exception e) {
            log.error("发送设备控制命令异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
    
    /**
     * 发送16进制设备控制命令
     *
     * @param deviceIp 设备IP地址
     * @param port 设备控制端口
     * @param hexCommand 16进制控制命令
     * @return 发送结果
     */
    @PostMapping("/device/command/hex")
    public ResponseResult<String> sendDeviceHexCommand(
            @RequestParam String deviceIp,
            @RequestParam(defaultValue = "9000") Integer port,
            @RequestParam String hexCommand) {
        try {
            boolean result = udpService.sendDeviceHexCommand(deviceIp, port, hexCommand);
            if (result) {
                return ResponseResult.success("16进制设备控制命令发送成功");
            } else {
                return ResponseResult.fail("16进制设备控制命令发送失败");
            }
        } catch (Exception e) {
            log.error("发送16进制设备控制命令异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
    
    /**
     * 发现UDP设备
     *
     * @param port 设备发现端口
     * @param timeout 超时时间(毫秒)
     * @return 发现的设备数量
     */
    @GetMapping("/device/discover")
    @UserLog(operationType = "EXECUTE", operationModule = "DEVICE", description = "发现UDP设备")
    public ResponseResult<Map<String, Object>> discoverDevices(
            @RequestParam(defaultValue = "9000") Integer port,
            @RequestParam(defaultValue = "5000") Integer timeout) {
        try {
            int deviceCount = udpService.discoverDevices(port, timeout);
            
            Map<String, Object> result = new HashMap<>();
            result.put("deviceCount", deviceCount);
            result.put("message", String.format("发现了 %d 个UDP设备", deviceCount));
            
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("发现UDP设备异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
    
    /**
     * 获取UDP配置信息
     *
     * @return UDP配置信息
     */
    @GetMapping("/config")
    public ResponseResult<UdpConfig> getUdpConfig() {
        return ResponseResult.success(udpConfig);
    }
    
    /**
     * 转换字符串为16进制字符串
     * 
     * @param text 要转换的文本
     * @param charsetName 字符集名称
     * @return 16进制字符串
     */
    @GetMapping("/convert/toHex")
    public ResponseResult<Map<String, String>> convertToHex(
            @RequestParam String text,
            @RequestParam(defaultValue = "UTF-8") String charsetName) {
        try {
            Charset charset = Charset.forName(charsetName);
            byte[] bytes = text.getBytes(charset);
            String hexString = udpService.bytesToHexString(bytes);
            
            Map<String, String> result = new HashMap<>();
            result.put("text", text);
            result.put("charset", charsetName);
            result.put("hex", hexString);
            
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("转换文本为16进制异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
    
    /**
     * 转换16进制字符串为普通文本
     * 
     * @param hexString 16进制字符串
     * @param charsetName 字符集名称
     * @return 转换后的文本
     */
    @GetMapping("/convert/fromHex")
    public ResponseResult<Map<String, String>> convertFromHex(
            @RequestParam String hexString,
            @RequestParam(defaultValue = "UTF-8") String charsetName) {
        try {
            byte[] bytes = udpService.hexStringToBytes(hexString);
            Charset charset = Charset.forName(charsetName);
            String text = new String(bytes, charset);
            
            Map<String, String> result = new HashMap<>();
            result.put("hex", hexString);
            result.put("charset", charsetName);
            result.put("text", text);
            
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("转换16进制为文本异常: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }
}
