package zb.iot.entity.VO;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 用户操作日志视图对象
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
@Setter
public class UserOperationLogVO {

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作类型名称
     */
    private String operationTypeName;

    /**
     * 操作模块
     */
    private String operationModule;

    /**
     * 操作模块名称
     */
    private String operationModuleName;

    /**
     * 操作详情描述
     */
    private String operationDetail;

    /**
     * 操作目标ID
     */
    private String targetId;

    /**
     * 操作目标名称
     */
    private String targetName;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理信息
     */
    private String userAgent;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 响应状态
     */
    private String responseStatus;

    /**
     * 响应状态名称
     */
    private String responseStatusName;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行耗时(毫秒)
     */
    private Integer executionTime;

    /**
     * 所属展厅ID
     */
    private Integer exhibitionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 格式化的创建时间
     */
    private String createTimeFormatted;
}
