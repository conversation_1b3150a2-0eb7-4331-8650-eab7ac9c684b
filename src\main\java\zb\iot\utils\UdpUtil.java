package zb.iot.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.concurrent.*;
import java.util.function.Consumer;

/**
 * UDP通信工具类
 * 支持单播、广播和异步处理UDP数据包
 */
@Slf4j
public class UdpUtil {

    /**
     * 默认缓冲区大小
     */
    private static final int DEFAULT_BUFFER_SIZE = 1024;
    
    /**
     * 默认超时时间(毫秒)
     */
    private static final int DEFAULT_TIMEOUT = 3000;
    
    /**
     * 用于异步处理的线程池
     */
    private static final ExecutorService executorService = Executors.newCachedThreadPool();
    
    /**
     * 存储UDP监听器
     */
    private static final ConcurrentHashMap<Integer, DatagramSocket> listenerSockets = new ConcurrentHashMap<>();

    /**
     * 发送UDP数据包(单播)
     *
     * @param host 目标主机地址
     * @param port 目标端口
     * @param data 要发送的数据
     * @return 是否发送成功
     */
    public static boolean sendPacket(String host, int port, byte[] data) {
        try {
            InetAddress address = InetAddress.getByName(host);
            DatagramPacket packet = new DatagramPacket(data, data.length, address, port);
            DatagramSocket socket = new DatagramSocket();
            
            socket.setSoTimeout(DEFAULT_TIMEOUT);
            socket.send(packet);
            socket.close();
            
            log.debug("UDP数据包发送成功: 主机={}, 端口={}, 数据大小={} 字节", host, port, data.length);
            return true;
        } catch (UnknownHostException e) {
            log.error("发送UDP数据包失败，无法解析主机地址: {}", e.getMessage());
        } catch (SocketException e) {
            log.error("发送UDP数据包失败，套接字异常: {}", e.getMessage());
        } catch (IOException e) {
            log.error("发送UDP数据包失败，IO异常: {}", e.getMessage());
        }
        return false;
    }
    
    /**
     * 使用指定字符编码发送UDP数据包(单播)
     *
     * @param host 目标主机地址
     * @param port 目标端口
     * @param message 要发送的消息
     * @param charset 字符编码
     * @return 是否发送成功
     */
    public static boolean sendPacket(String host, int port, String message, Charset charset) {
        byte[] data = message.getBytes(charset);
        return sendPacket(host, port, data);
    }
    
    /**
     * 发送16进制字符串表示的UDP数据包(单播)
     *
     * @param host 目标主机地址
     * @param port 目标端口
     * @param hexString 16进制字符串
     * @return 是否发送成功
     */
    public static boolean sendHexPacket(String host, int port, String hexString) {
        try {
            byte[] data = hexStringToByteArray(hexString);
            return sendPacket(host, port, data);
        } catch (IllegalArgumentException e) {
            log.error("发送16进制UDP数据包失败，16进制字符串格式错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 发送UDP数据包(广播)
     *
     * @param port 广播端口
     * @param data 要发送的数据
     * @return 是否发送成功
     */
    public static boolean broadcastPacket(int port, byte[] data) {
        try {
            InetAddress broadcastAddress = InetAddress.getByName("***************");
            DatagramPacket packet = new DatagramPacket(data, data.length, broadcastAddress, port);
            DatagramSocket socket = new DatagramSocket();
            
            socket.setBroadcast(true);
            socket.send(packet);
            socket.close();
            
            log.debug("UDP广播数据包发送成功: 端口={}, 数据大小={} 字节", port, data.length);
            return true;
        } catch (UnknownHostException e) {
            log.error("发送UDP广播数据包失败，无法解析广播地址: {}", e.getMessage());
        } catch (SocketException e) {
            log.error("发送UDP广播数据包失败，套接字异常: {}", e.getMessage());
        } catch (IOException e) {
            log.error("发送UDP广播数据包失败，IO异常: {}", e.getMessage());
        }
        return false;
    }
    
    /**
     * 使用指定字符编码广播UDP数据包
     *
     * @param port 广播端口
     * @param message 要发送的消息
     * @param charset 字符编码
     * @return 是否发送成功
     */
    public static boolean broadcastPacket(int port, String message, Charset charset) {
        byte[] data = message.getBytes(charset);
        return broadcastPacket(port, data);
    }
    
    /**
     * 广播16进制字符串表示的UDP数据包
     *
     * @param port 广播端口
     * @param hexString 16进制字符串
     * @return 是否发送成功
     */
    public static boolean broadcastHexPacket(int port, String hexString) {
        try {
            byte[] data = hexStringToByteArray(hexString);
            return broadcastPacket(port, data);
        } catch (IllegalArgumentException e) {
            log.error("发送16进制UDP广播数据包失败，16进制字符串格式错误: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 发送UDP多播数据包
     *
     * @param multicastAddress 多播组地址
     * @param port 多播端口
     * @param data 要发送的数据
     * @param ttl 多播TTL值
     * @return 是否发送成功
     */
    public static boolean multicastPacket(String multicastAddress, int port, byte[] data, int ttl) {
        try {
            InetAddress groupAddress = InetAddress.getByName(multicastAddress);
            if (!groupAddress.isMulticastAddress()) {
                log.error("发送UDP多播数据包失败，不是有效的多播地址: {}", multicastAddress);
                return false;
            }
            
            MulticastSocket socket = new MulticastSocket();
            socket.setTimeToLive(ttl);
            
            DatagramPacket packet = new DatagramPacket(data, data.length, groupAddress, port);
            socket.send(packet);
            socket.close();
            
            log.debug("UDP多播数据包发送成功: 多播组={}, 端口={}, 数据大小={} 字节", multicastAddress, port, data.length);
            return true;
        } catch (UnknownHostException e) {
            log.error("发送UDP多播数据包失败，无法解析多播地址: {}", e.getMessage());
        } catch (SocketException e) {
            log.error("发送UDP多播数据包失败，套接字异常: {}", e.getMessage());
        } catch (IOException e) {
            log.error("发送UDP多播数据包失败，IO异常: {}", e.getMessage());
        }
        return false;
    }
    
    /**
     * 使用指定字符编码发送UDP多播数据包
     *
     * @param multicastAddress 多播组地址
     * @param port 多播端口
     * @param message 要发送的消息
     * @param charset 字符编码
     * @param ttl 多播TTL值
     * @return 是否发送成功
     */
    public static boolean multicastPacket(String multicastAddress, int port, String message, Charset charset, int ttl) {
        byte[] data = message.getBytes(charset);
        return multicastPacket(multicastAddress, port, data, ttl);
    }
    
    /**
     * 发送16进制字符串表示的UDP多播数据包
     *
     * @param multicastAddress 多播组地址
     * @param port 多播端口
     * @param hexString 16进制字符串
     * @param ttl 多播TTL值
     * @return 是否发送成功
     */
    public static boolean multicastHexPacket(String multicastAddress, int port, String hexString, int ttl) {
        try {
            byte[] data = hexStringToByteArray(hexString);
            return multicastPacket(multicastAddress, port, data, ttl);
        } catch (IllegalArgumentException e) {
            log.error("发送16进制UDP多播数据包失败，16进制字符串格式错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 接收UDP数据包(同步)
     *
     * @param port 监听端口
     * @param timeout 超时时间(毫秒)，若为0则使用默认值
     * @return 接收到的数据，如果超时或出错则返回null
     */
    public static byte[] receivePacket(int port, int timeout) {
        DatagramSocket socket = null;
        try {
            socket = new DatagramSocket(port);
            socket.setSoTimeout(timeout > 0 ? timeout : DEFAULT_TIMEOUT);
            
            byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
            DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
            
            socket.receive(packet);
            
            byte[] data = new byte[packet.getLength()];
            System.arraycopy(packet.getData(), packet.getOffset(), data, 0, packet.getLength());
            
            log.debug("接收到UDP数据包: 来源={}, 数据大小={} 字节", 
                     packet.getAddress().getHostAddress() + ":" + packet.getPort(), data.length);
            return data;
        } catch (SocketTimeoutException e) {
            log.debug("接收UDP数据包超时: 端口={}, 超时={} ms", port, timeout);
        } catch (SocketException e) {
            log.error("接收UDP数据包失败，套接字异常: {}", e.getMessage());
        } catch (IOException e) {
            log.error("接收UDP数据包失败，IO异常: {}", e.getMessage());
        } finally {
            if (socket != null && !socket.isClosed()) {
                socket.close();
            }
        }
        return null;
    }
    
    /**
     * 接收UDP数据包并以指定字符集解析为字符串(同步)
     *
     * @param port 监听端口
     * @param timeout 超时时间(毫秒)，若为0则使用默认值
     * @param charset 字符编码
     * @return 接收到的消息，如果超时或出错则返回null
     */
    public static String receiveMessage(int port, int timeout, Charset charset) {
        byte[] data = receivePacket(port, timeout);
        if (data != null) {
            return new String(data, charset);
        }
        return null;
    }
    
    /**
     * 接收UDP数据包并解析为16进制字符串(同步)
     *
     * @param port 监听端口
     * @param timeout 超时时间(毫秒)，若为0则使用默认值
     * @return 接收到的16进制字符串，如果超时或出错则返回null
     */
    public static String receiveHexString(int port, int timeout) {
        byte[] data = receivePacket(port, timeout);
        if (data != null) {
            return byteArrayToHexString(data);
        }
        return null;
    }

    /**
     * 启动UDP监听器(异步)
     *
     * @param port 监听端口
     * @param dataHandler 数据处理回调，接收参数为数据包对象
     * @return 是否成功启动监听
     */
    public static boolean startListener(int port, Consumer<DatagramPacket> dataHandler) {
        // 检查端口是否已被使用
        if (listenerSockets.containsKey(port)) {
            log.warn("UDP监听器已存在: 端口={}", port);
            return false;
        }
        
        try {
            DatagramSocket socket = new DatagramSocket(port);
            listenerSockets.put(port, socket);
            
            // 使用线程池处理异步监听
            executorService.submit(() -> {
                try {
                    log.info("UDP监听器已启动: 端口={}", port);
                    byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
                    
                    while (!socket.isClosed()) {
                        try {
                            DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
                            socket.receive(packet);
                            
                            // 提交到线程池中处理接收到的数据
                            final DatagramPacket finalPacket = packet;
                            executorService.submit(() -> {
                                try {
                                    dataHandler.accept(finalPacket);
                                } catch (Exception e) {
                                    log.error("处理UDP数据包异常: {}", e.getMessage());
                                }
                            });
                        } catch (SocketException e) {
                            if (socket.isClosed()) {
                                log.info("UDP监听器已关闭: 端口={}", port);
                                break;
                            }
                            log.error("UDP监听器套接字异常: {}", e.getMessage());
                        } catch (IOException e) {
                            log.error("UDP监听器IO异常: {}", e.getMessage());
                        }
                    }
                } finally {
                    if (!socket.isClosed()) {
                        socket.close();
                    }
                    listenerSockets.remove(port);
                    log.info("UDP监听器已停止: 端口={}", port);
                }
            });
            
            return true;
        } catch (SocketException e) {
            log.error("启动UDP监听器失败: 端口={}, 错误={}", port, e.getMessage());
            return false;
        }
    }
    
    /**
     * 启动UDP字符串消息监听器(异步)
     *
     * @param port 监听端口
     * @param messageHandler 消息处理回调，接收参数为字符串消息
     * @param charset 字符编码
     * @return 是否成功启动监听
     */
    public static boolean startMessageListener(int port, Consumer<String> messageHandler, Charset charset) {
        return startListener(port, packet -> {
            byte[] data = extractData(packet);
            String message = new String(data, charset);
            String sourceAddress = getSourceAddress(packet);
            
            log.debug("收到UDP消息: 来源={}, 消息={}", sourceAddress, message);
            
            try {
                messageHandler.accept(message);
            } catch (Exception e) {
                log.error("处理UDP字符串消息异常: {}", e.getMessage(), e);
            }
        });
    }
    
    /**
     * 启动UDP十六进制消息监听器(异步)
     *
     * @param port 监听端口
     * @param hexHandler 十六进制消息处理回调，接收参数为十六进制字符串
     * @return 是否成功启动监听
     */
    public static boolean startHexListener(int port, Consumer<String> hexHandler) {
        return startListener(port, packet -> {
            byte[] data = extractData(packet);
            String hexString = byteArrayToHexString(data);
            String sourceAddress = getSourceAddress(packet);
            
            log.debug("收到UDP十六进制消息: 来源={}, 消息={}", sourceAddress, hexString);
            
            try {
                hexHandler.accept(hexString);
            } catch (Exception e) {
                log.error("处理UDP十六进制消息异常: {}", e.getMessage(), e);
            }
        });
    }

    /**
     * 停止UDP监听器
     *
     * @param port 监听端口
     * @return 是否成功停止
     */
    public static boolean stopListener(int port) {
        DatagramSocket socket = listenerSockets.get(port);
        if (socket != null && !socket.isClosed()) {
            socket.close();
            listenerSockets.remove(port);
            log.info("UDP监听器已停止: 端口={}", port);
            return true;
        } else {
            log.warn("UDP监听器不存在或已关闭: 端口={}", port);
            return false;
        }
    }

    /**
     * 停止所有UDP监听器
     */
    public static void stopAllListeners() {
        for (DatagramSocket socket : listenerSockets.values()) {
            if (!socket.isClosed()) {
                socket.close();
            }
        }
        listenerSockets.clear();
        log.info("所有UDP监听器已停止");
    }
    
    /**
     * 解析接收到的UDP数据包
     *
     * @param packet 接收到的数据包
     * @return 数据包中的数据
     */
    public static byte[] extractData(DatagramPacket packet) {
        byte[] data = new byte[packet.getLength()];
        System.arraycopy(packet.getData(), packet.getOffset(), data, 0, packet.getLength());
        return data;
    }
    
    /**
     * 获取数据包的源地址
     *
     * @param packet 数据包
     * @return 源地址字符串 (IP:端口)
     */
    public static String getSourceAddress(DatagramPacket packet) {
        return packet.getAddress().getHostAddress() + ":" + packet.getPort();
    }
    
    /**
     * 关闭UDP工具资源
     * 在应用程序关闭时调用此方法释放资源
     */
    public static void shutdown() {
        stopAllListeners();
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("UDP工具资源已释放");
    }
    
    /**
     * 将字节数组转换为16进制字符串
     *
     * @param bytes 字节数组
     * @return 16进制字符串
     */
    public static String byteArrayToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) {
                sb.append('0');
            }
            sb.append(hex);
        }
        return sb.toString().toUpperCase();
    }
    
    /**
     * 将16进制字符串转换为字节数组
     *
     * @param hexString 16进制字符串
     * @return 字节数组
     * @throws IllegalArgumentException 如果输入的不是有效的16进制字符串
     */
    public static byte[] hexStringToByteArray(String hexString) {
        String cleanHex = hexString.replaceAll("\\s", "").toUpperCase();
        if (!cleanHex.matches("^[0-9A-F]*$")) {
            throw new IllegalArgumentException("无效的16进制字符串: " + hexString);
        }
        
        // 如果长度为奇数，前面补0
        if (cleanHex.length() % 2 != 0) {
            cleanHex = "0" + cleanHex;
        }
        
        int len = cleanHex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(cleanHex.charAt(i), 16) << 4)
                    + Character.digit(cleanHex.charAt(i + 1), 16));
        }
        System.out.println("转换后的字节数组: " + Arrays.toString(data));
        return data;
    }
}