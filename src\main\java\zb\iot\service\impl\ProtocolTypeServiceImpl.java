package zb.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import zb.iot.entity.Device;
import zb.iot.entity.Exhibition;
import zb.iot.entity.ProtocolType;
import zb.iot.mapper.ExhibitionMapper;
import zb.iot.mapper.ProtocolTypeMapper;
import zb.iot.service.IExhibitionService;
import zb.iot.service.IProtocolTypeService;

import java.util.List;

@Service
public class ProtocolTypeServiceImpl extends ServiceImpl<ProtocolTypeMapper, ProtocolType> implements IProtocolTypeService {
    @Override
    public List<ProtocolType> getProtocolTypeList() {
        return list();
    }


}
