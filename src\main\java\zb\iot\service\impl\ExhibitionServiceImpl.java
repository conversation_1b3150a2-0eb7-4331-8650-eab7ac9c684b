package zb.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import zb.iot.controller.dto.ExhibitionDTO;
import zb.iot.entity.Exhibition;
import zb.iot.mapper.ExhibitionMapper;
import zb.iot.mapper.UserMapper;
import zb.iot.service.IExhibitionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import zb.iot.utils.OssStorageUtil;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 展厅信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExhibitionServiceImpl extends ServiceImpl<ExhibitionMapper, Exhibition> implements IExhibitionService {

    @Value("${file.upload.dir}")
    private String uploadDir;
    private final UserMapper userMapper;
    private final OssStorageUtil ossStorageUtil;

    @Override
    public IPage<ExhibitionDTO> getExhibitionPage(Integer pageNum, Integer pageSize, String name) {
        // 1. 执行基础分页查询
        LambdaQueryWrapper<Exhibition> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(name)) {
            queryWrapper.like(Exhibition::getName, name);
        }
        queryWrapper.orderByDesc(Exhibition::getCreateTime);

        IPage<Exhibition> exhibitionPage = this.page(new Page<>(pageNum, pageSize), queryWrapper);

        // 2. 转换结果为DTO，处理Logo URL
        List<ExhibitionDTO> records = exhibitionPage.getRecords().stream()
                .map(exhibition -> {
                    // 获取可访问的Logo URL
                    // String logoUrl = ossStorageUtil.getPublicUrl(exhibition.getLogoPath());
                    String logo = getLogoUrl(exhibition.getLogoPath());
                    return ExhibitionDTO.fromEntity(exhibition, logo);
                })
                .collect(Collectors.toList());

        // 3. 创建新的分页对象
        IPage<ExhibitionDTO> dtoPage = new Page<>(
                exhibitionPage.getCurrent(),
                exhibitionPage.getSize(),
                exhibitionPage.getTotal());
        dtoPage.setRecords(records);

        return dtoPage;
    }

    @Override
    public boolean addExhibition(ExhibitionDTO exhibitionDTO, MultipartFile logo) {

        try{
            // 保存文件到本地
            String fileName = System.currentTimeMillis() + "_" + logo.getOriginalFilename();
            Path filePath = Paths.get(uploadDir, fileName);
            Files.createDirectories(filePath.getParent());
            Files.copy(logo.getInputStream(), filePath);

            // 2. 创建展厅对象
            Exhibition exhibition = new Exhibition();
            exhibition.setName(exhibitionDTO.getName());
            exhibition.setAddress(exhibitionDTO.getAddress());
            exhibition.setDescription(exhibitionDTO.getDescription());
            exhibition.setExhibitionIP(exhibitionDTO.getExhibitionIP());
            exhibition.setLogoPath(fileName);

            exhibition.setStatus((byte) 1);

            return this.save(exhibition);
        }catch (IOException e){
            log.error("图片上传失败: {}", e.getMessage(), e);
            return false;
        }catch (Exception e){
            log.error("添加展厅失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateExhibition(Exhibition exhibition) {
        return false;
    }

    @Override
    public boolean removeExhibitionById(Integer id) {
        return false;
    }

    private String getLogoUrl(String logoPath) {
        return "http://localhost:8080/api/" + logoPath; // 假设你的服务运行在 8080 端口
    }
}
