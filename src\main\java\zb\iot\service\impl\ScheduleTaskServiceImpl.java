package zb.iot.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;
import zb.iot.entity.Strategy;
import zb.iot.service.IScheduleTaskService;
import zb.iot.service.IStrategyExecutionService;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

/**
 * 定时任务服务实现类
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduleTaskServiceImpl implements IScheduleTaskService {

    private final TaskScheduler taskScheduler;
    private final IStrategyExecutionService strategyExecutionService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    // 存储活跃的定时任务
    private final ConcurrentHashMap<Integer, ScheduledFuture<?>> activeTasks = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, ScheduleTaskInfo> taskInfos = new ConcurrentHashMap<>();

    @Override
    public boolean createScheduleTask(Strategy strategy) {
        try {
            log.info("创建定时任务，策略ID: {}, 策略名称: {}", strategy.getId(), strategy.getName());

            // 解析策略配置
            JsonNode config = objectMapper.readTree(strategy.getConfig());
            String cronExpression = buildCronExpression(config);

            if (cronExpression == null) {
                log.error("无法构建Cron表达式，策略ID: {}", strategy.getId());
                return false;
            }

            // 取消已存在的任务
            cancelScheduleTask(strategy.getId());

            // 创建新任务
            ScheduledFuture<?> scheduledFuture = taskScheduler.schedule(
                () -> executeScheduledStrategy(strategy),
                new CronTrigger(cronExpression)
            );

            // 保存任务信息
            activeTasks.put(strategy.getId(), scheduledFuture);
            
            ScheduleTaskInfo taskInfo = new ScheduleTaskInfo(
                strategy.getId(),
                strategy.getName(),
                cronExpression,
                getNextExecutionTime(cronExpression),
                null,
                "ACTIVE",
                "SCHEDULE"
            );
            taskInfos.put(strategy.getId(), taskInfo);

            log.info("定时任务创建成功，策略ID: {}, Cron表达式: {}", strategy.getId(), cronExpression);
            return true;

        } catch (Exception e) {
            log.error("创建定时任务失败，策略ID: {}", strategy.getId(), e);
            return false;
        }
    }

    @Override
    public boolean cancelScheduleTask(Integer strategyId) {
        try {
            ScheduledFuture<?> task = activeTasks.remove(strategyId);
            if (task != null) {
                task.cancel(false);
                taskInfos.remove(strategyId);
                log.info("定时任务已取消，策略ID: {}", strategyId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("取消定时任务失败，策略ID: {}", strategyId, e);
            return false;
        }
    }

    @Override
    public boolean updateScheduleTask(Strategy strategy) {
        // 先取消旧任务，再创建新任务
        cancelScheduleTask(strategy.getId());
        return createScheduleTask(strategy);
    }

    @Override
    public List<ScheduleTaskInfo> getActiveTasks() {
        return new ArrayList<>(taskInfos.values());
    }

    @Override
    public boolean triggerScheduleTask(Integer strategyId) {
        try {
            // 这里需要获取策略对象并执行
            // 暂时返回true，实际实现需要调用策略执行服务
            log.info("手动触发定时任务，策略ID: {}", strategyId);
            return true;
        } catch (Exception e) {
            log.error("手动触发定时任务失败，策略ID: {}", strategyId, e);
            return false;
        }
    }

    @Override
    public boolean hasActiveTask(Integer strategyId) {
        return activeTasks.containsKey(strategyId);
    }

    /**
     * 执行定时策略
     */
    private void executeScheduledStrategy(Strategy strategy) {
        try {
            log.info("执行定时策略，策略ID: {}, 策略名称: {}", strategy.getId(), strategy.getName());
            
            // 更新最后执行时间
            ScheduleTaskInfo taskInfo = taskInfos.get(strategy.getId());
            if (taskInfo != null) {
                taskInfo.setLastExecutionTime(LocalDateTime.now());
            }

            // 执行策略（这里调用实际的策略执行逻辑）
            strategyExecutionService.executeScheduleStrategy(strategy, "SCHEDULED");

        } catch (Exception e) {
            log.error("执行定时策略失败，策略ID: {}", strategy.getId(), e);
        }
    }

    /**
     * 构建Cron表达式
     */
    private String buildCronExpression(JsonNode config) {
        try {
            String scheduleType = config.get("scheduleType").asText();
            String executeTime = config.get("executeTime").asText(); // 格式: "HH:mm:ss"

            LocalTime time = LocalTime.parse(executeTime);
            int hour = time.getHour();
            int minute = time.getMinute();
            int second = time.getSecond();

            switch (scheduleType) {
                case "daily":
                    // 每天执行
                    return String.format("%d %d %d * * ?", second, minute, hour);
                case "workday":
                    // 工作日执行 (周一到周五)
                    return String.format("%d %d %d ? * MON-FRI", second, minute, hour);
                case "weekend":
                    // 周末执行
                    return String.format("%d %d %d ? * SAT,SUN", second, minute, hour);
                case "weekly":
                    // 每周执行，需要指定星期几
                    String dayOfWeek = config.has("dayOfWeek") ? config.get("dayOfWeek").asText() : "MON";
                    return String.format("%d %d %d ? * %s", second, minute, hour, dayOfWeek);
                case "monthly":
                    // 每月执行，需要指定日期
                    int dayOfMonth = config.has("dayOfMonth") ? config.get("dayOfMonth").asInt() : 1;
                    return String.format("%d %d %d %d * ?", second, minute, hour, dayOfMonth);
                default:
                    log.warn("不支持的定时类型: {}", scheduleType);
                    return null;
            }
        } catch (Exception e) {
            log.error("构建Cron表达式失败", e);
            return null;
        }
    }

    /**
     * 获取下次执行时间
     */
    private LocalDateTime getNextExecutionTime(String cronExpression) {
        try {
            CronExpression cron = CronExpression.parse(cronExpression);
            return cron.next(LocalDateTime.now());
        } catch (Exception e) {
            log.error("计算下次执行时间失败", e);
            return null;
        }
    }
}
