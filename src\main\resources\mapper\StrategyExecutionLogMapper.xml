<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zb.iot.mapper.StrategyExecutionLogMapper">

    <!-- 分页查询策略执行记录 -->
    <select id="selectExecutionLogPage" resultType="zb.iot.entity.StrategyExecutionLog">
        SELECT * FROM strategy_execution_log
        WHERE 1=1
        <if test="strategyId != null">
            AND strategy_id = #{strategyId}
        </if>
        <if test="startTime != null">
            AND execution_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND execution_time &lt;= #{endTime}
        </if>
        ORDER BY execution_time DESC
    </select>

    <!-- 查询策略最近的执行记录 -->
    <select id="selectRecentExecutionLogs" resultType="zb.iot.entity.StrategyExecutionLog">
        SELECT * FROM strategy_execution_log
        WHERE strategy_id = #{strategyId}
        ORDER BY execution_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计策略执行情况 -->
    <select id="selectExecutionStatistics" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as count,
            AVG(execution_duration) as avg_duration
        FROM strategy_execution_log
        WHERE strategy_id = #{strategyId}
        <if test="startTime != null">
            AND execution_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND execution_time &lt;= #{endTime}
        </if>
        GROUP BY status
    </select>

    <!-- 清理过期的执行记录 -->
    <delete id="deleteExpiredLogs">
        DELETE FROM strategy_execution_log
        WHERE create_time &lt; #{beforeTime}
    </delete>

</mapper>
