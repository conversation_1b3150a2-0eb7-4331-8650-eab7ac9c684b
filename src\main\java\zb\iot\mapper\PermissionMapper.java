package zb.iot.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import zb.iot.entity.Permission;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface PermissionMapper extends BaseMapper<Permission> {

    List<String> selectPermissionsByUserId(Integer id);
}
