package zb.iot.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zb.iot.entity.PolicyTag;
import zb.iot.mapper.PolicyTagMapper;
import zb.iot.service.IPolicyTagService;

import java.util.List;


/**
 * 权限表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Service
public class PolicyTagServiceImpl extends ServiceImpl<PolicyTagMapper, PolicyTag> implements IPolicyTagService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addTag(PolicyTag policyTag) {
        // 保存标签
        return this.save(policyTag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTag(Integer tagId, String name, String description) {
        // 获取标签
        PolicyTag tag = this.getById(tagId);
        if (tag == null) {
            throw new RuntimeException("标签不存在");
        }

        // 更新标签
        tag.setTagName(name);
        tag.setTagDescription(description);

        return this.updateById(tag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteTags(List<Integer> tagIds) {
        if (tagIds == null || tagIds.isEmpty()) {
            return false;
        }

        // 批量删除并返回删除数量
        return this.removeBatchByIds(tagIds);
    }

    @Override
    public IPage<PolicyTag> pageTags(Integer pageNum, Integer pageSize, String name, Integer exhibitionId) {
        Page<PolicyTag> page = new Page<>(pageNum, pageSize);

        LambdaQueryWrapper<PolicyTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyTag::getExhibitionId, exhibitionId);
        if (name != null && !name.isEmpty()) {
            queryWrapper.like(PolicyTag::getTagName, name);
        }

        return this.page(page, queryWrapper);
    }

    @Override
    public boolean updateStatus(Integer tagId, Byte status) {
        // 获取标签
        PolicyTag tag = this.getById(tagId);
        if (tag == null) {
            throw new RuntimeException("标签不存在");
        }

        // 更新标签
        tag.setStatus(status);
        return this.updateById(tag);
    }

    @Override
    public List<PolicyTag> getAllByExhibition(Integer exhibitionId) {

        LambdaQueryWrapper<PolicyTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyTag::getExhibitionId, exhibitionId);


        return this.list(queryWrapper);
    }
}
