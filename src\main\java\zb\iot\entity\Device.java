package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 物联网设备表
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
@TableName("device")
public class Device implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属展厅ID
     */
    @TableField("exhibition_id")
    private Integer exhibitionId;

    /**
     * 设备名称
     */
    @TableField("name")
    private String name;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * MAC地址
     */
    @TableField("mac_address")
    private String macAddress;

    @TableField("port")
    private Integer port;

    /**
     * 设备状态(0:离线,1:在线)
     */
    @TableField("status")
    private Byte status;

    /**
     * 设备标识
     */
    @TableField("identifier")
    private String identifier;

    /**
     * 设备类型
     */
    @TableField("device_type_id")
    private Integer deviceTypeId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 设备关联的权限列表 - 非数据库字段
     * 用于传输数据，不直接映射到数据库
     */
    @TableField(exist = false)
    private List<PolicyTag> policyTag;

    @TableField("enable_command")
    private String enableCommand;

    @TableField("disable_command")
    private String disableCommand;

    @TableField("protocol_type_id")
    private Integer protocolTypeId;
}
