package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.lettuce.core.StrAlgoArgs;
import lombok.Getter;
import lombok.Setter;

/**
 * 视频资源表
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
@TableName("video")
public class Video implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 视频ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属展厅ID
     */
    @TableField("exhibition_id")
    private Integer exhibitionId;

    /**
     * 视频名称
     */
    @TableField("name")
    private String name;

    /**
     * 视频文件路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 视频时长(秒)
     */
    @TableField("duration")
    private Integer duration;

    /**
     * 上传时间
     */
    @TableField(value = "upload_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime uploadTime;

    /**
     * 状态(1:可用,0:不可用)
     */
    @TableField("status")
    private Byte status;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;
}
