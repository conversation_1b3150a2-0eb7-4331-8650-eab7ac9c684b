<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zb.iot.mapper.AdministratorMapper">

    <resultMap id="AdminWithPermissionsMap" type="zb.iot.entity.Administrator">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="exhibitionId" column="exhibition_id"/>
        <result property="createTime" column="create_time"/>
        <collection property="permissions" ofType="java.lang.Integer">
            <result column="permission_id"/>
        </collection>
    </resultMap>

    <select id="getAdminWithPermissions" resultMap="AdminWithPermissionsMap">
        SELECT
            a.id, a.user_id, a.exhibition_id, a.create_time,
            p.id AS permission_id
        FROM
            administrator a
                LEFT JOIN
            admin_permission ap ON a.id = ap.admin_id
                LEFT JOIN
            permission p ON ap.permission_id = p.id
        WHERE
            a.id = #{adminId}
    </select>

    <!-- 查询展厅的所有管理员 -->
    <select id="getAdminsByExhibitionId" resultType="zb.iot.controller.dto.AdminInfoDTO">
        SELECT
            a.user_id,
            u.username,
            u.avatar_path AS avatarUrl,
            u.status,
            u.register_time
        FROM
            administrator a
                JOIN
            user u ON a.user_id = u.id
        WHERE
            a.exhibition_id = #{exhibitionId}
        ORDER BY
            a.create_time DESC
    </select>

    <!-- 根据用户ID获取管理员ID -->
    <select id="getAdminIdByUserId" resultType="java.lang.Integer">
        SELECT id FROM administrator WHERE user_id = #{userId}
    </select>

    <select id="getNonAdminUsersByExhibitionId" resultType="zb.iot.controller.dto.AdminInfoDTO">
        SELECT
            u.id as userId,
            u.username,
            u.avatar_path AS avatarUrl,
            u.register_time
        FROM
            user u
        WHERE
            u.id NOT IN (
                SELECT a.user_id
                FROM administrator a
                WHERE a.exhibition_id = #{exhibitionId}
            )
        ORDER BY
            u.register_time DESC
    </select>

</mapper>
