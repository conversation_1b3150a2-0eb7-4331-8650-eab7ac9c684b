package zb.iot.controller.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 用户日志查询DTO
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
@Setter
public class
UserLogQueryDTO {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作模块
     */
    private String operationModule;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 响应状态
     */
    private String responseStatus;

    /**
     * 展厅ID
     */
    private Integer exhibitionId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 关键词搜索（用户名、操作详情、目标名称）
     */
    private String keyword;
}
