package zb.iot.annotation;

import java.lang.annotation.*;

/**
 * 用户操作日志注解
 * <AUTHOR>
 * @since 2025-01-31
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface UserLog {

    /**
     * 操作类型
     */
    String operationType() default "";

    /**
     * 操作模块
     */
    String operationModule() default "";

    /**
     * 操作描述
     */
    String description() default "";

    /**
     * 是否记录请求参数
     */
    boolean recordParams() default true;

    /**
     * 是否记录响应结果
     */
    boolean recordResult() default false;
}
