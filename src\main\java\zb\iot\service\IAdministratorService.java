package zb.iot.service;

import zb.iot.controller.dto.AdminInfoDTO;
import zb.iot.entity.Administrator;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * 管理员表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface IAdministratorService extends IService<Administrator> {

    /**
     * 添加管理员并分配权限
     *
     * @param exhibitionId 展厅ID
     * @param userId 用户ID
     * @param permissionIds 权限ID列表
     * @throws RuntimeException 如果用户已经是管理员或添加失败
     */
    Administrator addAdminWithPermissions(Integer exhibitionId, Integer userId, List<Integer> permissionIds);

    /**
     * 根据ID获取管理员，包含权限信息
     *
     * @param adminId 管理员ID
     * @return 管理员对象，包含权限信息
     */
    Administrator getAdministratorWithPermissions(Integer adminId);

    /**
     * 更新管理员权限
     *
     * @param adminId 管理员ID
     * @param permissionIds 新的权限ID列表
     * @return 更新后的管理员对象，包含权限信息
     */
    Administrator updateAdministratorPermissions(Integer adminId, List<Integer> permissionIds);

    /**
     * 检查用户是否已是指定展厅的管理员
     *
     * @param exhibitionId 展厅ID
     * @param userId 用户ID
     * @return 如果已是管理员返回true，否则返回false
     */
    Administrator getAdministratorByExhibitionAndUser(Integer exhibitionId, Integer userId);

    /**
     * 根据展厅ID获取所有管理员
     *
     * @param exhibitionId 展厅ID
     * @return 管理员信息列表
     */
    List<AdminInfoDTO> getAdminsByExhibitionId(Integer exhibitionId);

    /**
     * 根据展厅ID获取所有非管理员用户
     *
     * @param exhibitionId 展厅ID
     * @return 非管理员用户信息列表
     */
    List<AdminInfoDTO> getNonAdminUsersByExhibitionId(Integer exhibitionId);


}
