package zb.iot.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import zb.iot.entity.Strategy;
import zb.iot.service.ILinkageMonitorService;
import zb.iot.service.IStrategyExecutionService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 联动监听服务实现类
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LinkageMonitorServiceImpl implements ILinkageMonitorService {

    private final IStrategyExecutionService strategyExecutionService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    // 存储活跃的联动监听
    private final ConcurrentHashMap<Integer, LinkageMonitorInfo> activeMonitors = new ConcurrentHashMap<>();
    // 按设备ID索引的监听器，用于快速查找
    private final ConcurrentHashMap<Integer, List<Integer>> deviceMonitors = new ConcurrentHashMap<>();

    @Override
    public boolean startLinkageMonitor(Strategy strategy) {
        try {
            log.info("启动联动监听，策略ID: {}, 策略名称: {}", strategy.getId(), strategy.getName());

            // 解析策略配置
            JsonNode config = objectMapper.readTree(strategy.getConfig());
            Integer triggerDeviceId = config.get("triggerDeviceId").asInt();
            String triggerCondition = config.get("triggerCondition").asText();
            Object triggerValue = config.get("triggerValue").asText();

            // 停止已存在的监听
            stopLinkageMonitor(strategy.getId());

            // 创建新的监听信息
            LinkageMonitorInfo monitorInfo = new LinkageMonitorInfo(
                strategy.getId(),
                strategy.getName(),
                triggerDeviceId,
                triggerCondition,
                triggerValue,
                "ACTIVE",
                LocalDateTime.now(),
                null,
                0
            );

            // 保存监听信息
            activeMonitors.put(strategy.getId(), monitorInfo);

            // 建立设备ID到策略ID的索引
            deviceMonitors.computeIfAbsent(triggerDeviceId, k -> new ArrayList<>()).add(strategy.getId());

            log.info("联动监听启动成功，策略ID: {}, 触发设备ID: {}, 触发条件: {}", 
                    strategy.getId(), triggerDeviceId, triggerCondition);
            return true;

        } catch (Exception e) {
            log.error("启动联动监听失败，策略ID: {}", strategy.getId(), e);
            return false;
        }
    }

    @Override
    public boolean stopLinkageMonitor(Integer strategyId) {
        try {
            LinkageMonitorInfo monitorInfo = activeMonitors.remove(strategyId);
            if (monitorInfo != null) {
                // 从设备索引中移除
                Integer triggerDeviceId = monitorInfo.getTriggerDeviceId();
                List<Integer> strategies = deviceMonitors.get(triggerDeviceId);
                if (strategies != null) {
                    strategies.remove(strategyId);
                    if (strategies.isEmpty()) {
                        deviceMonitors.remove(triggerDeviceId);
                    }
                }
                log.info("联动监听已停止，策略ID: {}", strategyId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("停止联动监听失败，策略ID: {}", strategyId, e);
            return false;
        }
    }

    @Override
    public boolean updateLinkageMonitor(Strategy strategy) {
        // 先停止旧监听，再启动新监听
        stopLinkageMonitor(strategy.getId());
        return startLinkageMonitor(strategy);
    }

    @Override
    public List<LinkageMonitorInfo> getActiveMonitors() {
        return new ArrayList<>(activeMonitors.values());
    }

    @Override
    public boolean triggerLinkageStrategy(Integer strategyId, Object triggerData) {
        try {
            LinkageMonitorInfo monitorInfo = activeMonitors.get(strategyId);
            if (monitorInfo != null) {
                monitorInfo.setLastTriggerTime(LocalDateTime.now());
                monitorInfo.setTriggerCount(monitorInfo.getTriggerCount() + 1);
                
                log.info("手动触发联动策略，策略ID: {}", strategyId);
                // 这里需要调用策略执行服务
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("手动触发联动策略失败，策略ID: {}", strategyId, e);
            return false;
        }
    }

    @Override
    public boolean hasActiveMonitor(Integer strategyId) {
        return activeMonitors.containsKey(strategyId);
    }

    @Override
    public void handleDeviceStatusChange(Integer deviceId, String statusType, Object statusValue) {
        try {
            List<Integer> strategyIds = deviceMonitors.get(deviceId);
            if (strategyIds == null || strategyIds.isEmpty()) {
                return;
            }

            log.debug("处理设备状态变化，设备ID: {}, 状态类型: {}, 状态值: {}", deviceId, statusType, statusValue);

            for (Integer strategyId : strategyIds) {
                LinkageMonitorInfo monitorInfo = activeMonitors.get(strategyId);
                if (monitorInfo != null && checkTriggerCondition(monitorInfo, statusType, statusValue)) {
                    log.info("联动条件满足，触发策略执行，策略ID: {}, 设备ID: {}", strategyId, deviceId);
                    
                    // 更新触发信息
                    monitorInfo.setLastTriggerTime(LocalDateTime.now());
                    monitorInfo.setTriggerCount(monitorInfo.getTriggerCount() + 1);

                    // 异步执行策略，避免阻塞状态处理
                    executeLinkageStrategyAsync(strategyId);
                }
            }
        } catch (Exception e) {
            log.error("处理设备状态变化失败，设备ID: {}", deviceId, e);
        }
    }

    /**
     * 检查触发条件是否满足
     */
    private boolean checkTriggerCondition(LinkageMonitorInfo monitorInfo, String statusType, Object statusValue) {
        try {
            String triggerCondition = monitorInfo.getTriggerCondition();
            Object triggerValue = monitorInfo.getTriggerValue();

            switch (triggerCondition) {
                case "temperature_above":
                    return compareNumeric(statusValue, triggerValue, ">");
                case "temperature_below":
                    return compareNumeric(statusValue, triggerValue, "<");
                case "humidity_above":
                    return compareNumeric(statusValue, triggerValue, ">");
                case "humidity_below":
                    return compareNumeric(statusValue, triggerValue, "<");
                case "status_equals":
                    return statusValue.toString().equals(triggerValue.toString());
                case "status_changed":
                    return true; // 状态发生变化就触发
                case "device_online":
                    return "1".equals(statusValue.toString()) || "online".equals(statusValue.toString());
                case "device_offline":
                    return "0".equals(statusValue.toString()) || "offline".equals(statusValue.toString());
                default:
                    log.warn("未知的触发条件: {}", triggerCondition);
                    return false;
            }
        } catch (Exception e) {
            log.error("检查触发条件失败", e);
            return false;
        }
    }

    /**
     * 数值比较
     */
    private boolean compareNumeric(Object actualValue, Object expectedValue, String operator) {
        try {
            double actual = Double.parseDouble(actualValue.toString());
            double expected = Double.parseDouble(expectedValue.toString());

            switch (operator) {
                case ">":
                    return actual > expected;
                case "<":
                    return actual < expected;
                case ">=":
                    return actual >= expected;
                case "<=":
                    return actual <= expected;
                case "==":
                    return actual == expected;
                default:
                    return false;
            }
        } catch (NumberFormatException e) {
            log.error("数值比较失败，无法解析数值", e);
            return false;
        }
    }

    /**
     * 异步执行联动策略
     */
    private void executeLinkageStrategyAsync(Integer strategyId) {
        // 这里可以使用线程池异步执行
        new Thread(() -> {
            try {
                // 调用策略执行服务
                log.info("异步执行联动策略，策略ID: {}", strategyId);
                // strategyExecutionService.executeLinkageStrategyContent(strategyId, "LINKAGE_TRIGGERED");
            } catch (Exception e) {
                log.error("异步执行联动策略失败，策略ID: {}", strategyId, e);
            }
        }).start();
    }
}
