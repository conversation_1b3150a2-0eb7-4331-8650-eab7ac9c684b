package zb.iot.service;

import java.net.DatagramPacket;
import java.nio.charset.Charset;
import java.util.function.Consumer;

/**
 * UDP服务接口
 * 定义UDP通信的业务方法
 */
public interface IUdpService {
    
    /**
     * 发送UDP数据包
     *
     * @param host 目标主机
     * @param port 目标端口
     * @param message 消息内容
     * @return 是否发送成功
     */
    boolean sendMessage(String host, int port, String message);
    
    /**
     * 使用指定字符编码发送UDP数据包
     *
     * @param host 目标主机
     * @param port 目标端口
     * @param message 消息内容
     * @param charset 字符编码
     * @return 是否发送成功
     */
    boolean sendMessage(String host, int port, String message, Charset charset);
    
    /**
     * 发送16进制字符串表示的UDP数据包
     *
     * @param host 目标主机
     * @param port 目标端口
     * @param hexString 16进制字符串
     * @return 是否发送成功
     */
    boolean sendHexMessage(String host, int port, String hexString);
    
    /**
     * 发送UDP数据包
     *
     * @param host 目标主机
     * @param port 目标端口
     * @param data 二进制数据
     * @return 是否发送成功
     */
    boolean sendData(String host, int port, byte[] data);
    
    /**
     * 广播UDP消息
     *
     * @param port 广播端口
     * @param message 消息内容
     * @return 是否发送成功
     */
    boolean broadcastMessage(int port, String message);
    
    /**
     * 使用指定字符编码广播UDP消息
     *
     * @param port 广播端口
     * @param message 消息内容
     * @param charset 字符编码
     * @return 是否发送成功
     */
    boolean broadcastMessage(int port, String message, Charset charset);
    
    /**
     * 广播16进制字符串表示的UDP消息
     *
     * @param port 广播端口
     * @param hexString 16进制字符串
     * @return 是否发送成功
     */
    boolean broadcastHexMessage(int port, String hexString);
    
    /**
     * 多播UDP消息
     *
     * @param multicastAddress 多播组地址
     * @param port 端口
     * @param message 消息内容
     * @return 是否发送成功
     */
    boolean multicastMessage(String multicastAddress, int port, String message);
    
    /**
     * 使用指定字符编码多播UDP消息
     *
     * @param multicastAddress 多播组地址
     * @param port 端口
     * @param message 消息内容
     * @param charset 字符编码
     * @return 是否发送成功
     */
    boolean multicastMessage(String multicastAddress, int port, String message, Charset charset);
    
    /**
     * 多播16进制字符串表示的UDP消息
     *
     * @param multicastAddress 多播组地址
     * @param port 端口
     * @param hexString 16进制字符串
     * @return 是否发送成功
     */
    boolean multicastHexMessage(String multicastAddress, int port, String hexString);
    
    /**
     * 接收UDP数据包(同步)
     *
     * @param port 监听端口
     * @param timeout 超时时间(毫秒)
     * @return 接收到的消息内容，超时返回null
     */
    String receiveMessage(int port, int timeout);
    
    /**
     * 接收UDP数据包并使用指定字符集解析(同步)
     *
     * @param port 监听端口
     * @param timeout 超时时间(毫秒)
     * @param charset 字符编码
     * @return 接收到的消息内容，超时返回null
     */
    String receiveMessage(int port, int timeout, Charset charset);
    
    /**
     * 接收UDP数据包并解析为16进制字符串(同步)
     *
     * @param port 监听端口
     * @param timeout 超时时间(毫秒)
     * @return 接收到的16进制字符串，超时返回null
     */
    String receiveHexMessage(int port, int timeout);
    
    /**
     * 启动UDP监听器
     *
     * @param port 监听端口
     * @param messageHandler 消息处理回调
     * @return 是否成功启动
     */
    boolean startListener(int port, Consumer<String> messageHandler);
    
    /**
     * 启动UDP监听器并使用指定字符集解析消息
     *
     * @param port 监听端口
     * @param messageHandler 消息处理回调
     * @param charset 字符编码
     * @return 是否成功启动
     */
    boolean startListener(int port, Consumer<String> messageHandler, Charset charset);
    
    /**
     * 启动UDP十六进制消息监听器
     *
     * @param port 监听端口
     * @param hexHandler 十六进制消息处理回调
     * @return 是否成功启动
     */
    boolean startHexListener(int port, Consumer<String> hexHandler);
    
    /**
     * 启动UDP数据包监听器
     *
     * @param port 监听端口
     * @param packetHandler 数据包处理回调
     * @return 是否成功启动
     */
    boolean startPacketListener(int port, Consumer<DatagramPacket> packetHandler);
    
    /**
     * 停止指定端口的UDP监听器
     *
     * @param port 监听端口
     * @return 是否成功停止
     */
    boolean stopListener(int port);
    
    /**
     * 停止所有UDP监听器
     */
    void stopAllListeners();
    
    /**
     * 发送设备控制命令
     * 
     * @param deviceIp 设备IP地址
     * @param port 设备控制端口
     * @param command 控制命令
     * @return 是否发送成功
     */
    boolean sendDeviceCommand(String deviceIp, int port, String command);
    
    /**
     * 发送16进制格式的设备控制命令
     * 
     * @param deviceIp 设备IP地址
     * @param port 设备控制端口
     * @param hexCommand 16进制格式的控制命令
     * @return 是否发送成功
     */
    boolean sendDeviceHexCommand(String deviceIp, int port, String hexCommand);
    
    /**
     * 发现网络中的UDP设备
     * 
     * @param discoveryPort 设备发现端口
     * @param timeout 超时时间(毫秒)
     * @return 发现的设备数量
     */
    int discoverDevices(int discoveryPort, int timeout);
    
    /**
     * 将字节数组转换为16进制字符串
     *
     * @param bytes 字节数组
     * @return 16进制字符串
     */
    String bytesToHexString(byte[] bytes);
    
    /**
     * 将16进制字符串转换为字节数组
     *
     * @param hexString 16进制字符串
     * @return 字节数组
     * @throws IllegalArgumentException 如果输入的不是有效的16进制字符串
     */
    byte[] hexStringToBytes(String hexString) throws IllegalArgumentException;

    /**
     * 测试UDP连接
     *
     * @param host 目标主机
     * @param port 目标端口
     * @return 连接是否可用
     */
    boolean testConnection(String host, int port);

    /**
     * Ping主机
     *
     * @param host 目标主机
     * @return 主机是否可达
     */
    boolean pingHost(String host);

    /**
     * 测试UDP端口是否开放
     *
     * @param host 目标主机
     * @param port 目标端口
     * @param timeout 超时时间(毫秒)
     * @return 端口是否开放
     */
    boolean isPortOpen(String host, int port, int timeout);
}