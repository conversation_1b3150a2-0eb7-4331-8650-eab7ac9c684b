package zb.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import zb.iot.entity.StrategyExecutionLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 策略执行记录表 Mapper 接口
 * <AUTHOR>
 * @since 2025-01-31
 */
@Mapper
public interface StrategyExecutionLogMapper extends BaseMapper<StrategyExecutionLog> {

    /**
     * 分页查询策略执行记录
     * @param page 分页参数
     * @param strategyId 策略ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行记录列表
     */
    IPage<StrategyExecutionLog> selectExecutionLogPage(
            Page<StrategyExecutionLog> page,
            @Param("strategyId") Integer strategyId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询策略最近的执行记录
     * @param strategyId 策略ID
     * @param limit 限制数量
     * @return 执行记录列表
     */
    List<StrategyExecutionLog> selectRecentExecutionLogs(@Param("strategyId") Integer strategyId, @Param("limit") Integer limit);

    /**
     * 统计策略执行情况
     * @param strategyId 策略ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Object> selectExecutionStatistics(
            @Param("strategyId") Integer strategyId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 清理过期的执行记录
     * @param beforeTime 时间点
     * @return 删除数量
     */
    int deleteExpiredLogs(@Param("beforeTime") LocalDateTime beforeTime);
}
