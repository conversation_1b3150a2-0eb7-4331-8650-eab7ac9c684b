package zb.iot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import zb.iot.annotation.UserLog;
import zb.iot.common.ResponseResult;
import zb.iot.entity.Device;
import zb.iot.entity.ProtocolType;
import zb.iot.entity.VO.DeviceVO;
import zb.iot.service.IDeviceService;
import zb.iot.service.IProtocolTypeService;

import java.util.List;

/**
 * 物联网设备表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Slf4j
@RestController
@RequestMapping("/devices")
@RequiredArgsConstructor
public class DeviceController {

    private final IDeviceService deviceService;

    private final IProtocolTypeService protocolTypeService;

    /**
     * 获取设备列表
     *
     * @param exhibitionId 设备ID
     * @return 设备策略标签列表
     */
    @GetMapping("/{exhibitionId}")
    public ResponseResult<List<Device>> getDeviceList(@PathVariable Integer exhibitionId) {
        List<Device> deviceList = deviceService.getDeviceList(exhibitionId);

        return ResponseResult.success(deviceList);
    }

    /**
     * 获取协议列表
     * @return 协议列表
     */
    @GetMapping("/getProtocolTypeList")
    public ResponseResult<List<ProtocolType>> getProtocolTypeList() {
        List<ProtocolType> protocolTypeList = protocolTypeService.getProtocolTypeList();

        return ResponseResult.success(protocolTypeList);
    }


    /**
     * 添加设备
     *
     * @param device 设备信息（包含名称、IP、标识和策略标签列表）
     * @return 添加结果
     */
    @PostMapping("/add")
    @UserLog(operationType = "CREATE", operationModule = "DEVICE", description = "添加设备")
    public ResponseResult<String> addDevice(@Validated @RequestBody Device device) {
        try {
            Boolean result = deviceService.addDevice(device);
            if (result) {
                return ResponseResult.success();
            } else {
                return ResponseResult.fail("设备添加失败");
            }
        } catch (Exception e) {
            log.error("添加设备失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 更新设备
     *
     * @param device 设备信息（包含名称、IP、状态、标识和策略标签列表）
     * @return 更新结果
     */
    @PostMapping("/update")
    @UserLog(operationType = "UPDATE", operationModule = "DEVICE", description = "更新设备")
    public ResponseResult<String> updateDevice(@Validated @RequestBody Device device) {
        try {
            Boolean result = deviceService.updateDevice(device);
            if (result) {
                return ResponseResult.success();
            } else {
                return ResponseResult.fail("设备更新失败");
            }
        } catch (Exception e) {
            log.error("更新设备失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 更新设备状态
     *
     * @param device 设备信息（包含名称、IP、状态、标识和策略标签列表）
     * @return 更新结果
     */
    @PostMapping("/updateStatus")
    public ResponseResult<String> updateDeviceStatus(@RequestBody Device device) {
        try {
            Boolean result = deviceService.updateDeviceStatus(device.getId(), device.getStatus());
            if (result) {
                return ResponseResult.success();
            } else {
                return ResponseResult.fail("设备状态更新失败");
            }
        }catch (Exception e) {
            log.error("更新设备状态失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 删除设备
     *
     * @param id 设备ID
     * @return 删除结果
     */
    @DeleteMapping("/del/{id}")
    @UserLog(operationType = "DELETE", operationModule = "DEVICE", description = "删除设备")
    public ResponseResult<String> deleteDevice(@PathVariable Integer id) {
        try {
            boolean result = deviceService.deleteDevice(id);
            if (result) {
                return ResponseResult.success();
            } else {
                return ResponseResult.fail("设备删除失败，设备可能不存在");
            }
        } catch (Exception e) {
            log.error("删除设备失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 获取设备详情
     *
     * @param id 设备ID
     * @return 设备详情
     */
    @GetMapping("/getDeviceDetail/{id}")
    public ResponseResult<DeviceVO> getDevice(@PathVariable Integer id) {
        try {
            DeviceVO deviceVO = deviceService.getDeviceById(id);
            if (deviceVO != null) {
                return ResponseResult.success(deviceVO);
            } else {
                return ResponseResult.fail("设备不存在");
            }
        } catch (Exception e) {
            log.error("获取设备详情失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 分页查询设备
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 设备名称（可选，用于模糊查询）
     * @return 分页设备列表
     */
    @GetMapping("/page/{exhibitionId}")
    public ResponseResult<IPage<Device>> pageDevices(@PathVariable Integer exhibitionId,
                                                     @RequestParam(defaultValue = "1") Integer pageNum,
                                                     @RequestParam(defaultValue = "10") Integer pageSize,
                                                     @RequestParam(required = false) String name) {
        try {
            IPage<Device> devicePage = deviceService.pageDevices(pageNum, pageSize, name, exhibitionId);
            return ResponseResult.success(devicePage);
        } catch (Exception e) {
            log.error("分页查询设备失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 网络唤醒设备
     *
     * @param id 设备ID
     * @return 唤醒结果
     */
    @PostMapping("/wakeOnLan/{id}")
    @UserLog(operationType = "EXECUTE", operationModule = "DEVICE", description = "网络唤醒设备")
    public ResponseResult<String> wakeOnLan(@PathVariable Integer id) {
        try {
            boolean result = deviceService.wakeOnLan(id);
            if (result) {
                return ResponseResult.success("设备唤醒成功");
            } else {
                return ResponseResult.fail("设备唤醒失败，请检查设备MAC地址是否正确");
            }
        } catch (Exception e) {
            log.error("设备唤醒失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }



}
