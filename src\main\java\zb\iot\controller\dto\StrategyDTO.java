package zb.iot.controller.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;



/**
 * 策略数据传输对象
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
@Setter
public class StrategyDTO {

    /**
     * 策略ID（更新时需要）
     */
    private Integer id;

    /**
     * 所属展厅ID
     */
    @NotNull(message = "展厅ID不能为空")
    private Integer exhibitionId;

    /**
     * 策略名称
     */
    @NotBlank(message = "策略名称不能为空")
    private String name;

    /**
     * 策略类型(1:群组,2:定时,3:联动)
     */
    @NotNull(message = "策略类型不能为空")
    private Integer type;

    /**
     * 策略描述
     */
    private String description;

    /**
     * 策略配置(JSON格式)
     */
    @NotBlank(message = "策略配置不能为空")
    private String config;

    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status = 1; // 默认启用
}
