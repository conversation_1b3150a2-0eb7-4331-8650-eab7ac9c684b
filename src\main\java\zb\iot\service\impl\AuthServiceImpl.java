package zb.iot.service.impl;

import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zb.iot.controller.dto.AuthResponse;
import zb.iot.controller.dto.LoginRequest;
import zb.iot.controller.dto.RegisterRequest;
import zb.iot.controller.dto.WechatLoginRequest;
import zb.iot.entity.Administrator;
import zb.iot.entity.User;
import zb.iot.mapper.AdministratorMapper;
import zb.iot.mapper.UserMapper;
import zb.iot.security.CustomUserDetails;
import zb.iot.security.JwtUtils;
import zb.iot.service.IAuthService;
import zb.iot.service.IWechatService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements IAuthService {

    private static final Logger log = LoggerFactory.getLogger(AuthServiceImpl.class);
    private final AuthenticationManager authenticationManager;
    private final UserMapper userMapper;
    private final AdministratorMapper administratorMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtils jwtUtils;
    private final IWechatService wechatService;

    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@(.+)$");
    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    @Override
    public AuthResponse login(LoginRequest request) {
        // 创建认证令牌
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUserIdentifier(), request.getPassword())
        );
        
        // 设置认证信息到安全上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // 获取用户详情
        CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();

        // 创建额外的JWT声明
        Map<String, Object> extraClaims = new HashMap<>();
        extraClaims.put("userId", userDetails.getUserId());
        
        // 生成JWT令牌
        String token = jwtUtils.generateToken(userDetails, extraClaims);
        
        // 更新最后登录时间
        User user = new User();
        user.setId(userDetails.getUserId());
        userMapper.updateById(user);

        List<Integer> adminPermissions = getAdminPermissions(userDetails.getUserId());
        
        // 构建响应
        return AuthResponse.builder()
                .token(token)
                .userId(userDetails.getUserId())
                .permissions(adminPermissions)
                .permissions(adminPermissions)
                .build();
    }

    @Override
    @Transactional
    public Boolean register(RegisterRequest request) {
        // 验证用户信息是否已存在
        validateRegisterRequest(request);
        
        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setPhone(request.getPhone());
        user.setEmail(request.getEmail());
        user.setStatus((byte) 1); // 默认启用
        user.setInvitationCode(request.getInvitationCode());
        
        // 保存用户
        return userMapper.insert(user) == 1;
    }

    @Override
    @Transactional
    public AuthResponse wechatLogin(WechatLoginRequest request) {
        // 调用微信API获取用户信息
        Map<String, Object> wechatInfo = wechatService.getWechatUserInfo(request.getCode());
        
        // 获取微信用户标识
        String openId = wechatInfo.get("openid").toString();
        String unionId = wechatInfo.get("unionid") != null ? wechatInfo.get("unionid").toString() : null;
        
        // 查找或创建用户
        User user = wechatService.getUserByWechatOpenId(openId, unionId, wechatInfo);
        
        // 更新最后登录时间
        User updateUser = new User();
        updateUser.setId(user.getId());
        updateUser.setLastLoginTime(LocalDateTime.now());
        userMapper.updateById(updateUser);
        
        // 创建用户详情
        CustomUserDetails userDetails = CustomUserDetails.fromUser(user, null);
        
        // 创建额外的JWT声明
        Map<String, Object> extraClaims = new HashMap<>();
        extraClaims.put("userId", user.getId());
        
        // 生成JWT令牌
        String token = jwtUtils.generateToken(userDetails, extraClaims);
        
        return AuthResponse.builder()
                .token(token)
                .userId(user.getId())
                .build();
    }
    
    // 验证注册请求
    private void validateRegisterRequest(RegisterRequest request) {
        // 验证用户名是否已存在
        if (userMapper.countByUsername(request.getUsername()) > 0) {
            throw new IllegalArgumentException("用户名已存在");
        }
        
        // 验证手机号（如果提供）
        if (request.getPhone() != null && !request.getPhone().isEmpty()) {
            if (userMapper.countByPhone(request.getPhone()) > 0) {
                throw new IllegalArgumentException("手机号已被注册");
            }
        }
        
        // 验证邮箱（如果提供）
        if (request.getEmail() != null && !request.getEmail().isEmpty()) {
            if (userMapper.countByEmail(request.getEmail()) > 0) {
                throw new IllegalArgumentException("邮箱已被注册");
            }
        }
        
        // 确保至少提供了手机号或邮箱之一
        if ((request.getPhone() == null || request.getPhone().isEmpty()) && 
            (request.getEmail() == null || request.getEmail().isEmpty())) {
            throw new IllegalArgumentException("手机号和邮箱必须至少提供一个");
        }
    }

    /**
     * 获取用户的管理员权限
     * @param userId 用户ID
     * @return 权限ID列表，如果不是管理员则返回null
     */
    private List<Integer> getAdminPermissions(Integer userId) {
        // 查询用户是否为管理员
        Integer adminId = administratorMapper.getAdminIdByUserId(userId);
        if (adminId == null) {
            // 用户不是管理员，返回null
            return null;
        }
        Administrator admin = administratorMapper.getAdminWithPermissions(adminId);
        if (admin.getPermissions() == null || admin.getPermissions().isEmpty()) {
            log.warn("管理员ID为{}的用户没有分配权限", adminId);
            return new ArrayList<>(); // 返回空列表
        }
        // 用户是管理员，查询其拥有的权限
        return admin.getPermissions();
    }

} 