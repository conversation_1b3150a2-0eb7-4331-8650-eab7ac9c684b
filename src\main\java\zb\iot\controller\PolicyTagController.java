package zb.iot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import zb.iot.annotation.UserLog;
import zb.iot.common.ResponseResult;
import zb.iot.entity.PolicyTag;
import zb.iot.service.IPolicyTagService;
import java.util.List;

/**
 * 策略标签管理器
 */
@RestController
@RequestMapping("/policyTag")
@RequiredArgsConstructor
@Slf4j
public class PolicyTagController {

    private final IPolicyTagService policyTagService;

    /**
     * 查询所属展厅下所有标签
     */
    @GetMapping("/getAllByExhibition/{exhibitionId}")
    public ResponseResult<List<PolicyTag>> getAllByExhibition(@PathVariable Integer exhibitionId) {
        try {
            List<PolicyTag> policyTags = policyTagService.getAllByExhibition(exhibitionId);
            return ResponseResult.success(policyTags);
        } catch (Exception e) {
            log.error("查询标签列表失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 添加标签
     */
    @PostMapping("/add")
    @UserLog(operationType = "CREATE", operationModule = "STRATEGY", description = "添加策略标签")
    public ResponseResult<String> addTag(@RequestBody PolicyTag policyTag) {
        try {
            if (!policyTagService.addTag(policyTag)){
                return ResponseResult.fail("添加失败");
            }
            return ResponseResult.success();
        } catch (Exception e) {
            log.error("添加标签失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 更新标签
     */
    @PutMapping("/update/{tagId}")
    @UserLog(operationType = "UPDATE", operationModule = "STRATEGY", description = "更新策略标签")
    public ResponseResult<String> updateTag(@RequestBody PolicyTag policyTag) {
        try {
            if (!policyTagService.updateTag(policyTag.getId(), policyTag.getTagName(), policyTag.getTagDescription())){
                return ResponseResult.fail("修改失败");
            }
            return ResponseResult.success();
        } catch (Exception e) {
            log.error("更新标签失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 批量删除标签
     */
    @DeleteMapping("/removeBatch")
    @UserLog(operationType = "DELETE", operationModule = "STRATEGY", description = "批量删除策略标签")
    public ResponseResult<Integer> batchDeleteTags(@RequestParam List<Integer> Ids) {
        try {
            if (Ids == null || Ids.isEmpty()) {
                return ResponseResult.fail("标签ID列表不能为空");
            }

            if (!policyTagService.batchDeleteTags(Ids)){
                return ResponseResult.fail("删除标签失败，请再次尝试");
            }
            return ResponseResult.success();
        } catch (Exception e) {
            log.error("批量删除标签失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 分页查询所属展厅下标签
     */
    @GetMapping("/page/{exhibitionId}")
    public ResponseResult<IPage<PolicyTag>> pageTags(
            @PathVariable Integer exhibitionId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name) {
        try {
            IPage<PolicyTag> page = policyTagService.pageTags(pageNum, pageSize, name, exhibitionId);
            return ResponseResult.success(page);
        } catch (Exception e) {
            log.error("查询标签列表失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 修改标签使用状态
     */
    @PutMapping("/updateStatus/{tagId}")
    @UserLog(operationType = "UPDATE", operationModule = "STRATEGY", description = "更新标签使用状态")
    public ResponseResult<String> updateStatus(@PathVariable Integer tagId,
                                               @RequestParam Byte status){

        try {
            if (!policyTagService.updateStatus(tagId, status)){
                return ResponseResult.fail("修改失败");
            }
            return ResponseResult.success();
        } catch (Exception e) {
            log.error("更新标签状态失败: {}", e.getMessage(), e);
            return ResponseResult.fail(e.getMessage());
        }
    }

}
