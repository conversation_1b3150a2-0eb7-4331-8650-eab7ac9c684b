package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 定时任务表
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
@TableName("schedule")
public class Schedule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private Integer deviceId;

    /**
     * 任务名称
     */
    @TableField("name")
    private String name;

    /**
     * 任务类型
     */
    @TableField("type")
    private String type;

    /**
     * 执行时间
     */
    @TableField("execution_time")
    private LocalDateTime executionTime;

    /**
     * 任务状态(0:待执行,1:已执行,2:失败)
     */
    @TableField("status")
    private Boolean status;

    /**
     * 任务内容(JSON格式)
     */
    @TableField("content")
    private String content;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;
}
