package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户操作日志表
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
@Setter
@TableName("user_operation_log")
public class UserOperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 操作类型(LOGIN,LOGOUT,CREATE,UPDATE,DELETE,EXECUTE,VIEW)
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作模块(STRATEGY,DEVICE,USER,EXHIBITION,SYSTEM)
     */
    @TableField("operation_module")
    private String operationModule;

    /**
     * 操作详情描述
     */
    @TableField("operation_detail")
    private String operationDetail;

    /**
     * 操作目标ID
     */
    @TableField("target_id")
    private String targetId;

    /**
     * 操作目标名称
     */
    @TableField("target_name")
    private String targetName;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理信息
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 请求URL
     */
    @TableField("request_url")
    private String requestUrl;

    /**
     * 请求方法(GET,POST,PUT,DELETE)
     */
    @TableField("request_method")
    private String requestMethod;

    /**
     * 请求参数(JSON格式)
     */
    @TableField("request_params")
    private String requestParams;

    /**
     * 响应状态(SUCCESS,FAILED,ERROR)
     */
    @TableField("response_status")
    private String responseStatus;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 执行耗时(毫秒)
     */
    @TableField("execution_time")
    private Integer executionTime;

    /**
     * 所属展厅ID
     */
    @TableField("exhibition_id")
    private Integer exhibitionId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    // 操作类型常量
    public static final String OPERATION_LOGIN = "LOGIN";           // 登录
    public static final String OPERATION_LOGOUT = "LOGOUT";         // 登出
    public static final String OPERATION_CREATE = "CREATE";         // 创建
    public static final String OPERATION_UPDATE = "UPDATE";         // 更新
    public static final String OPERATION_DELETE = "DELETE";         // 删除
    public static final String OPERATION_EXECUTE = "EXECUTE";       // 执行
    public static final String OPERATION_VIEW = "VIEW";             // 查看
    public static final String OPERATION_EXPORT = "EXPORT";         // 导出
    public static final String OPERATION_IMPORT = "IMPORT";         // 导入

    // 操作模块常量
    public static final String MODULE_STRATEGY = "STRATEGY";        // 策略模块
    public static final String MODULE_DEVICE = "DEVICE";            // 设备模块
    public static final String MODULE_USER = "USER";                // 用户模块
    public static final String MODULE_EXHIBITION = "EXHIBITION";    // 展厅模块
    public static final String MODULE_SYSTEM = "SYSTEM";            // 系统模块
    public static final String MODULE_AUTH = "AUTH";                // 认证模块

    // 响应状态常量
    public static final String STATUS_SUCCESS = "SUCCESS";          // 成功
    public static final String STATUS_FAILED = "FAILED";            // 失败
    public static final String STATUS_ERROR = "ERROR";              // 错误
}
