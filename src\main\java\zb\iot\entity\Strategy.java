package zb.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 策略表
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
@Setter
@TableName("strategy")
public class Strategy implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 策略ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属展厅ID
     */
    @TableField("exhibition_id")
    private Integer exhibitionId;

    /**
     * 策略名称
     */
    @TableField("name")
    private String name;

    /**
     * 策略类型(1:群组,2:定时,3:联动)
     */
    @TableField("type")
    private Integer type;

    /**
     * 策略描述
     */
    @TableField("description")
    private String description;

    /**
     * 策略配置(JSON格式)
     */
    @TableField("config")
    private String config;

    /**
     * 状态(0:禁用,1:启用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 最后执行时间
     */
    @TableField("last_executed")
    private LocalDateTime lastExecuted;

    /**
     * 执行次数
     */
    @TableField("execution_count")
    private Integer executionCount;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;

    // 策略类型常量
    public static final int TYPE_GROUP = 1;     // 群组策略
    public static final int TYPE_SCHEDULE = 2;  // 定时策略
    public static final int TYPE_LINKAGE = 3;   // 联动策略

    // 策略状态常量
    public static final int STATUS_DISABLED = 0; // 禁用
    public static final int STATUS_ENABLED = 1;  // 启用
}
