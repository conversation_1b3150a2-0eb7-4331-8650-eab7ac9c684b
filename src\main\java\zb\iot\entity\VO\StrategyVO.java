package zb.iot.entity.VO;

import lombok.Getter;
import lombok.Setter;
import zb.iot.entity.Device;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 策略视图对象
 * <AUTHOR>
 * @since 2025-01-31
 */
@Getter
@Setter
public class StrategyVO {

    /**
     * 策略ID
     */
    private Integer id;

    /**
     * 所属展厅ID
     */
    private Integer exhibitionId;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 策略类型(1:群组,2:定时,3:联动)
     */
    private Integer type;

    /**
     * 策略类型名称
     */
    private String typeName;

    /**
     * 策略描述
     */
    private String description;

    /**
     * 策略配置(JSON格式)
     */
    private String config;

    /**
     * 状态(0:禁用,1:启用)
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecuted;

    /**
     * 执行次数
     */
    private Integer executionCount;

    /**
     * 关联的设备列表（用于群组策略）
     */
    private List<Device> devices;

    /**
     * 下次执行时间（用于定时策略）
     */
    private LocalDateTime nextExecutionTime;

    /**
     * 是否可执行
     */
    private Boolean executable;

    /**
     * 最近执行结果
     */
    private String lastExecutionResult;
}
