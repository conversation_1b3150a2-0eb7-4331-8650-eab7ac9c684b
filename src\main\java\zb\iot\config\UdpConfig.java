package zb.iot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.DisposableBean;
import lombok.extern.slf4j.Slf4j;
import zb.iot.utils.UdpUtil;
import jakarta.annotation.PreDestroy;

/**
 * UDP配置类
 * 用于配置UDP相关参数和管理UDP资源的生命周期
 */
@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "udp")
public class UdpConfig implements DisposableBean {

    /**
     * 默认UDP监听端口
     */
    private int port = 9000;
    
    /**
     * 默认缓冲区大小
     */
    private int bufferSize = 1024;
    
    /**
     * 默认超时时间(毫秒)
     */
    private int timeout = 3000;
    
    /**
     * 是否自动启动监听器
     */
    private boolean autoStartListener = false;
    
    /**
     * 多播组地址
     */
    private String multicastAddress = "*********";
    
    /**
     * 多播TTL值
     */
    private int multicastTtl = 1;

    /**
     * 释放UDP资源
     * 当Spring容器关闭时，会自动调用此方法
     */
    @Override
    @PreDestroy
    public void destroy() {
        log.info("关闭UDP资源...");
        UdpUtil.shutdown();
    }
}
