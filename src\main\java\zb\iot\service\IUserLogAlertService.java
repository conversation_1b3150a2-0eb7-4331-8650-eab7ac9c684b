package zb.iot.service;

import zb.iot.entity.UserOperationLog;

/**
 * 用户日志告警服务接口
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface IUserLogAlertService {

    /**
     * 检查并处理日志告警
     * @param log 用户操作日志
     */
    void checkAndHandleAlert(UserOperationLog log);

    /**
     * 检查登录失败告警
     * @param username 用户名
     * @param ipAddress IP地址
     */
    void checkLoginFailureAlert(String username, String ipAddress);

    /**
     * 检查可疑IP告警
     * @param ipAddress IP地址
     */
    void checkSuspiciousIpAlert(String ipAddress);

    /**
     * 检查错误操作告警
     * @param userId 用户ID
     * @param username 用户名
     * @param ipAddress IP地址
     */
    void checkErrorOperationAlert(Integer userId, String username, String ipAddress);

    /**
     * 发送告警通知
     * @param alertType 告警类型
     * @param alertLevel 告警级别
     * @param title 告警标题
     * @param content 告警内容
     * @param userId 用户ID
     * @param username 用户名
     * @param ipAddress IP地址
     */
    void sendAlert(String alertType, String alertLevel, String title, String content, 
                   Integer userId, String username, String ipAddress);

    /**
     * 检查告警冷却时间
     * @param alertType 告警类型
     * @param key 告警键（如IP地址、用户名等）
     * @return true表示在冷却期内，false表示可以发送告警
     */
    boolean isInCooldown(String alertType, String key);
}
