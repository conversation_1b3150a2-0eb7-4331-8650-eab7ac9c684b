package zb.iot.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zb.iot.controller.dto.StrategyDTO;
import zb.iot.controller.dto.StrategyQueryDTO;
import zb.iot.entity.Strategy;
import zb.iot.entity.StrategyExecutionLog;
import zb.iot.entity.VO.StrategyVO;
import zb.iot.mapper.StrategyExecutionLogMapper;
import zb.iot.mapper.StrategyMapper;
import zb.iot.service.IScheduleTaskService;
import zb.iot.service.IStrategyExecutionService;
import zb.iot.service.IStrategyService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 策略管理服务实现类
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StrategyServiceImpl extends ServiceImpl<StrategyMapper, Strategy> implements IStrategyService {

    private final StrategyMapper strategyMapper;
    private final StrategyExecutionLogMapper executionLogMapper;
    private final IStrategyExecutionService strategyExecutionService;
    private final IScheduleTaskService scheduleTaskService;

    /**
     * 分页获取策略列表
     * @param queryDTO 查询条件封装对象
     * @return 策略分页结果
     */
    @Override
    public IPage<StrategyVO> getStrategyPage(StrategyQueryDTO queryDTO) {
        Page<StrategyVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        return strategyMapper.selectStrategyPage(page, queryDTO);
    }

    /**
     * 根据ID获取策略详情
     * @param id 策略ID
     * @return 策略详情VO对象
     */
    @Override
    public StrategyVO getStrategyById(Integer id) {
        return strategyMapper.selectStrategyById(id);
    }

    /**
     * 创建策略
     * @param strategyDTO 策略数据传输对象
     * @return 创建成功的策略对象
     * @throws RuntimeException 当策略配置验证失败或创建失败时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Strategy createStrategy(StrategyDTO strategyDTO) {
        // 验证策略配置
        if (!validateStrategyConfig(strategyDTO.getType(), strategyDTO.getConfig())) {
            throw new RuntimeException("策略配置验证失败");
        }

        Strategy strategy = new Strategy();
        strategy.setExhibitionId(strategyDTO.getExhibitionId());
        strategy.setName(strategyDTO.getName());
        strategy.setType(strategyDTO.getType());
        strategy.setDescription(strategyDTO.getDescription());
        strategy.setConfig(strategyDTO.getConfig());
        strategy.setStatus(strategyDTO.getStatus());
        strategy.setExecutionCount(0);

        if (save(strategy)) {
            log.info("创建策略成功，策略ID: {}, 策略名称: {}", strategy.getId(), strategy.getName());
            return strategy;
        } else {
            throw new RuntimeException("创建策略失败");
        }
    }

    /**
     * 更新策略
     * @param strategyDTO 策略数据传输对象
     * @return 更新后的策略对象
     * @throws RuntimeException 当策略ID为空、策略配置验证失败、策略不存在或更新失败时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Strategy updateStrategy(StrategyDTO strategyDTO) {
        if (strategyDTO.getId() == null) {
            throw new RuntimeException("策略ID不能为空");
        }

        // 验证策略配置
        if (!validateStrategyConfig(strategyDTO.getType(), strategyDTO.getConfig())) {
            throw new RuntimeException("策略配置验证失败");
        }

        Strategy strategy = getById(strategyDTO.getId());
        if (strategy == null) {
            throw new RuntimeException("策略不存在");
        }

        strategy.setName(strategyDTO.getName());
        strategy.setType(strategyDTO.getType());
        strategy.setDescription(strategyDTO.getDescription());
        strategy.setConfig(strategyDTO.getConfig());
        strategy.setStatus(strategyDTO.getStatus());

        if (updateById(strategy)) {
            log.info("更新策略成功，策略ID: {}, 策略名称: {}", strategy.getId(), strategy.getName());
            return strategy;
        } else {
            throw new RuntimeException("更新策略失败");
        }
    }

    /**
     * 删除策略
     * @param id 策略ID
     * @return 删除是否成功
     * @throws RuntimeException 当策略不存在或删除失败时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStrategy(Integer id) {
        Strategy strategy = getById(id);
        if (strategy == null) {
            throw new RuntimeException("策略不存在");
        }

        if (removeById(id)) {
            log.info("删除策略成功，策略ID: {}, 策略名称: {}", id, strategy.getName());
            return true;
        } else {
            throw new RuntimeException("删除策略失败");
        }
    }

    /**
     * 切换策略状态（启用/禁用）
     * @param id 策略ID
     * @param status 策略状态
     * @return 状态更新是否成功
     * @throws RuntimeException 当策略不存在或状态更新失败时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleStrategyStatus(Integer id, Integer status) {
        Strategy strategy = getById(id);
        if (strategy == null) {
            throw new RuntimeException("策略不存在");
        }

        Integer oldStatus = strategy.getStatus();
        strategy.setStatus(status);
        
        if (updateById(strategy)) {
            String statusText = status == Strategy.STATUS_ENABLED ? "启用" : "禁用";
            log.info("{}策略成功，策略ID: {}, 策略名称: {}", statusText, id, strategy.getName());
            
            // 如果是定时策略，根据状态变化处理定时任务
            if (strategy.getType() == Strategy.TYPE_SCHEDULE) {
                if (status == Strategy.STATUS_ENABLED && oldStatus == Strategy.STATUS_DISABLED) {
                    // 从禁用改为启用，创建定时任务
                    scheduleTaskService.createScheduleTask(strategy);
                } else if (status == Strategy.STATUS_DISABLED && oldStatus == Strategy.STATUS_ENABLED) {
                    // 从启用改为禁用，取消定时任务
                    scheduleTaskService.cancelScheduleTask(id);
                }
            }
            
            return true;
        } else {
            throw new RuntimeException("更新策略状态失败");
        }
    }

    /**
     * 执行策略（手动触发）
     * @param id 策略ID
     * @return 策略执行日志
     */
    @Override
    public StrategyExecutionLog executeStrategy(Integer id) {
        return executeStrategy(id, StrategyExecutionLog.TRIGGER_MANUAL);
    }

    /**
     * 执行策略
     * @param id 策略ID
     * @param triggerType 触发类型
     * @return 策略执行日志
     * @throws RuntimeException 当策略不存在或策略未启用时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public StrategyExecutionLog executeStrategy(Integer id, String triggerType) {
        Strategy strategy = getById(id);
        if (strategy == null) {
            throw new RuntimeException("策略不存在");
        }

        if (strategy.getStatus() != Strategy.STATUS_ENABLED) {
            throw new RuntimeException("策略未启用，无法执行");
        }

        log.info("开始执行策略，策略ID: {}, 策略名称: {}, 触发类型: {}", id, strategy.getName(), triggerType);

        // 执行策略
        StrategyExecutionLog executionLog = strategyExecutionService.executeStrategy(strategy, triggerType);

        // 更新策略执行信息
        strategyMapper.updateLastExecuted(id);

        return executionLog;
    }

    /**
     * 验证策略配置
     * @param type 策略类型
     * @param config 策略配置
     * @return 配置是否有效
     */
    @Override
    public boolean validateStrategyConfig(Integer type, String config) {
        return strategyExecutionService.validateStrategyConfig(type, config);
    }

    /**
     * 获取策略执行历史记录
     * @param strategyId 策略ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 执行历史记录分页结果
     */
    @Override
    public IPage<StrategyExecutionLog> getExecutionHistory(Integer strategyId, Integer pageNum, Integer pageSize) {
        Page<StrategyExecutionLog> page = new Page<>(pageNum, pageSize);
        return executionLogMapper.selectExecutionLogPage(page, strategyId, null, null);
    }

    /**
     * 获取指定展会下所有启用的策略
     * @param exhibitionId 展会ID
     * @return 启用的策略列表
     */
    @Override
    public List<Strategy> getEnabledStrategies(Integer exhibitionId) {
        return strategyMapper.selectEnabledStrategiesByExhibitionId(exhibitionId);
    }

    /**
     * 获取指定展会下特定类型的所有启用策略
     * @param exhibitionId 展会ID
     * @param type 策略类型
     * @return 启用的策略列表
     */
    @Override
    public List<Strategy> getEnabledStrategiesByType(Integer exhibitionId, Integer type) {
        return strategyMapper.selectEnabledStrategiesByType(exhibitionId, type);
    }

    /**
     * 批量执行策略
     * @param strategyIds 策略ID列表
     * @return 执行结果列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StrategyExecutionLog> batchExecuteStrategies(List<Integer> strategyIds) {
        List<StrategyExecutionLog> results = new ArrayList<>();
        
        for (Integer strategyId : strategyIds) {
            try {
                StrategyExecutionLog result = executeStrategy(strategyId);
                results.add(result);
            } catch (Exception e) {
                log.error("批量执行策略失败，策略ID: {}, 错误信息: {}", strategyId, e.getMessage());
                // 创建失败记录
                StrategyExecutionLog failedLog = new StrategyExecutionLog();
                failedLog.setStrategyId(strategyId);
                failedLog.setExecutionTime(LocalDateTime.now());
                failedLog.setStatus(StrategyExecutionLog.STATUS_FAILED);
                failedLog.setErrorMessage(e.getMessage());
                failedLog.setTriggerType(StrategyExecutionLog.TRIGGER_MANUAL);
                results.add(failedLog);
            }
        }
        
        return results;
    }
}
