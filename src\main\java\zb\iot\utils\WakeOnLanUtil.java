package zb.iot.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketException;
import java.net.UnknownHostException;

/**
 * 网络唤醒工具类
 * 用于发送魔术包唤醒支持WOL的设备
 */
@Slf4j
public class WakeOnLanUtil {

    /**
     * 默认WOL端口
     */
    private static final int PORT = 9;

    /**
     * 发送魔术包唤醒设备
     *
     * @param macAddress MAC地址，格式如：00:11:22:33:44:55 或 00-11-22-33-44-55
     * @return 是否成功发送魔术包
     */
    public static boolean wakeUp(String macAddress) {
        return wakeUp(macAddress, null);
    }

    /**
     * 发送魔术包唤醒设备
     *
     * @param macAddress MAC地址，格式如：00:11:22:33:44:55 或 00-11-22-33-44-55
     * @param ipAddress  IP地址，可选，如果为null则使用广播地址
     * @return 是否成功发送魔术包
     */
    public static boolean wakeUp(String macAddress, String ipAddress) {
        try {
            // 1. 格式化MAC地址
            byte[] macBytes = getMacBytes(macAddress);
            
            // 2. 构建魔术包
            byte[] magicPacket = buildMagicPacket(macBytes);
            
            // 3. 确定目标地址
            InetAddress address;
            if (ipAddress != null && !ipAddress.isEmpty()) {
                address = InetAddress.getByName(ipAddress);
            } else {
                // 使用广播地址
                address = InetAddress.getByName("***************");
            }
            
            // 4. 发送魔术包
            DatagramPacket packet = new DatagramPacket(magicPacket, magicPacket.length, address, PORT);
            DatagramSocket socket = new DatagramSocket();
            socket.setBroadcast(true);
            socket.send(packet);
            socket.close();
            
            log.info("成功发送唤醒包到MAC地址: {}, IP地址: {}", macAddress, address.getHostAddress());
            return true;
        } catch (UnknownHostException e) {
            log.error("网络唤醒失败，无法解析主机地址: {}", e.getMessage());
        } catch (SocketException e) {
            log.error("网络唤醒失败，套接字异常: {}", e.getMessage());
        } catch (IOException e) {
            log.error("网络唤醒失败，IO异常: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("网络唤醒失败，MAC地址格式错误: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 构建魔术包
     * 魔术包格式：6字节的0xFF，然后是16次重复的MAC地址
     *
     * @param macBytes MAC地址字节数组
     * @return 魔术包字节数组
     */
    private static byte[] buildMagicPacket(byte[] macBytes) {
        // 魔术包 = 6字节的0xFF + 16次重复的MAC地址（6字节）
        byte[] bytes = new byte[6 + 16 * macBytes.length];
        
        // 填充前6个字节为0xFF
        for (int i = 0; i < 6; i++) {
            bytes[i] = (byte) 0xFF;
        }
        
        // 填充16次MAC地址
        for (int i = 6; i < bytes.length; i += macBytes.length) {
            System.arraycopy(macBytes, 0, bytes, i, macBytes.length);
        }
        
        return bytes;
    }

    /**
     * 将MAC地址字符串转换为字节数组
     *
     * @param macAddress MAC地址字符串
     * @return MAC地址字节数组
     * @throws IllegalArgumentException 如果MAC地址格式不正确
     */
    private static byte[] getMacBytes(String macAddress) throws IllegalArgumentException {
        // 移除可能的分隔符
        String cleanMac = macAddress.replaceAll("[:-]", "");
        
        // 验证MAC地址格式
        if (cleanMac.length() != 12) {
            throw new IllegalArgumentException("无效的MAC地址: " + macAddress);
        }
        
        // 转换为字节数组
        byte[] bytes = new byte[6];
        for (int i = 0; i < 6; i++) {
            String hex = cleanMac.substring(i * 2, i * 2 + 2);
            bytes[i] = (byte) Integer.parseInt(hex, 16);
        }
        
        return bytes;
    }
} 