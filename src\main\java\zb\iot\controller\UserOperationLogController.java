package zb.iot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import zb.iot.annotation.UserLog;
import zb.iot.common.ResponseResult;
import zb.iot.controller.dto.UserLogQueryDTO;
import zb.iot.entity.VO.UserOperationLogVO;
import zb.iot.service.IUserOperationLogService;
import zb.iot.service.IUserLogExportService;

import jakarta.servlet.http.HttpServletResponse;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户操作日志控制器
 * <AUTHOR>
 * @since 2025-01-31
 */
@Slf4j
@RestController
@RequestMapping("/user-logs")
@RequiredArgsConstructor
public class UserOperationLogController {

    private final IUserOperationLogService userOperationLogService;
    private final IUserLogExportService userLogExportService;

    /**
     * 分页查询用户操作日志
     * @param queryDTO 查询条件
     * @return 日志分页数据
     */
    @GetMapping
    public ResponseResult<IPage<UserOperationLogVO>> getUserLogPage(UserLogQueryDTO queryDTO) {
        try {
            IPage<UserOperationLogVO> page = userOperationLogService.getUserLogPage(queryDTO);
            return ResponseResult.success(page);
        } catch (Exception e) {
            log.error("查询用户操作日志失败", e);
            return ResponseResult.fail("查询用户操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 查询用户最近的操作记录
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 操作记录列表
     */
    @GetMapping("/recent")
    public ResponseResult<List<UserOperationLogVO>> getRecentUserLogs(
            @RequestParam Integer userId,
            @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<UserOperationLogVO> logs = userOperationLogService.getRecentUserLogs(userId, limit);
            return ResponseResult.success(logs);
        } catch (Exception e) {
            log.error("查询用户最近操作记录失败，用户ID: {}", userId, e);
            return ResponseResult.fail("查询用户最近操作记录失败: " + e.getMessage());
        }
    }

    /**
     * 统计用户操作情况
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @GetMapping("/statistics/user")
    public ResponseResult<List<Map<String, Object>>> getUserOperationStatistics(
            @RequestParam Integer userId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<Map<String, Object>> statistics = userOperationLogService.getUserOperationStatistics(userId, startTime, endTime);
            return ResponseResult.success(statistics);
        } catch (Exception e) {
            log.error("统计用户操作情况失败，用户ID: {}", userId, e);
            return ResponseResult.fail("统计用户操作情况失败: " + e.getMessage());
        }
    }

    /**
     * 统计操作类型分布
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @GetMapping("/statistics/operation-type")
    public ResponseResult<List<Map<String, Object>>> getOperationTypeStatistics(
            @RequestParam(required = false) Integer exhibitionId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<Map<String, Object>> statistics = userOperationLogService.getOperationTypeStatistics(exhibitionId, startTime, endTime);
            return ResponseResult.success(statistics);
        } catch (Exception e) {
            log.error("统计操作类型分布失败", e);
            return ResponseResult.fail("统计操作类型分布失败: " + e.getMessage());
        }
    }

    /**
     * 统计操作模块分布
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @GetMapping("/statistics/operation-module")
    public ResponseResult<List<Map<String, Object>>> getOperationModuleStatistics(
            @RequestParam(required = false) Integer exhibitionId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<Map<String, Object>> statistics = userOperationLogService.getOperationModuleStatistics(exhibitionId, startTime, endTime);
            return ResponseResult.success(statistics);
        } catch (Exception e) {
            log.error("统计操作模块分布失败", e);
            return ResponseResult.fail("统计操作模块分布失败: " + e.getMessage());
        }
    }

    /**
     * 统计每日操作量
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @GetMapping("/statistics/daily")
    public ResponseResult<List<Map<String, Object>>> getDailyOperationStatistics(
            @RequestParam(required = false) Integer exhibitionId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<Map<String, Object>> statistics = userOperationLogService.getDailyOperationStatistics(exhibitionId, startTime, endTime);
            return ResponseResult.success(statistics);
        } catch (Exception e) {
            log.error("统计每日操作量失败", e);
            return ResponseResult.fail("统计每日操作量失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期的操作日志
     * @param days 保留天数
     * @return 清理结果
     */
    @DeleteMapping("/clean")
    @UserLog(operationType = "DELETE", operationModule = "SYSTEM", description = "清理过期用户日志")
    public ResponseResult<Integer> cleanExpiredLogs(@RequestParam(defaultValue = "90") Integer days) {
        try {
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(days);
            int deletedCount = userOperationLogService.cleanExpiredLogs(beforeTime);
            return ResponseResult.success(deletedCount);
        } catch (Exception e) {
            log.error("清理过期操作日志失败", e);
            return ResponseResult.fail("清理过期操作日志失败: " + e.getMessage());
        }
    }

    /**
     * 导出用户操作日志到Excel
     * @param queryDTO 查询条件
     * @param response HTTP响应
     */
    @GetMapping("/export")
    // @UserLog(operationType = "EXPORT", operationModule = "SYSTEM", description = "导出用户操作日志")
    public void exportToExcel(UserLogQueryDTO queryDTO, HttpServletResponse response) {
        userLogExportService.exportToExcel(queryDTO, response);
    }

    /**
     * 批量导出指定ID的日志
     * @param logIds 日志ID列表
     * @param response HTTP响应
     */
    @PostMapping("/export/batch")
    @UserLog(operationType = "EXPORT", operationModule = "SYSTEM", description = "批量导出用户操作日志")
    public void batchExportToExcel(@RequestBody List<Long> logIds, HttpServletResponse response) {
        userLogExportService.batchExportToExcel(logIds, response);
    }

    /**
     * 导出日志统计报告
     * @param exhibitionId 展厅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param response HTTP响应
     */
    @GetMapping("/export/statistics")
    @UserLog(operationType = "EXPORT", operationModule = "SYSTEM", description = "导出用户日志统计报告")
    public void exportStatisticsReport(
            @RequestParam(required = false) Integer exhibitionId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            HttpServletResponse response) {
        userLogExportService.exportStatisticsReport(exhibitionId, startTime, endTime, response);
    }
}
