-- MySQL dump 10.13  Distrib 8.0.19, for Win64 (x86_64)
--
-- Host: localhost    Database: exhibition_system
-- ------------------------------------------------------
-- Server version	8.0.41

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin_permission`
--

DROP TABLE IF EXISTS `admin_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_permission` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` int NOT NULL COMMENT '管理员ID',
  `permission_id` int NOT NULL COMMENT '权限ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `deleted` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_admin_permission` (`admin_id`,`permission_id`),
  KEY `idx_permission_id` (`permission_id`),
  KEY `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='管理员权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_permission`
--

LOCK TABLES `admin_permission` WRITE;
/*!40000 ALTER TABLE `admin_permission` DISABLE KEYS */;
INSERT INTO `admin_permission` VALUES (1,1,1,'2025-04-18 09:29:05',0),(2,1,2,'2025-04-18 09:29:05',0),(3,1,3,'2025-04-18 09:29:05',0),(4,1,4,'2025-04-18 09:29:05',0),(5,1,5,'2025-04-18 09:29:05',0),(6,2,1,'2025-04-18 09:34:04',0),(7,2,2,'2025-04-18 09:34:04',0),(8,2,3,'2025-04-18 09:34:04',0),(9,2,5,'2025-04-18 09:34:04',0),(10,2,4,'2025-04-18 09:34:04',0);
/*!40000 ALTER TABLE `admin_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `administrator`
--

DROP TABLE IF EXISTS `administrator`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `administrator` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `exhibition_id` int NOT NULL COMMENT '展厅ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_exhibition` (`user_id`,`exhibition_id`),
  KEY `idx_exhibition_id` (`exhibition_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='管理员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `administrator`
--

LOCK TABLES `administrator` WRITE;
/*!40000 ALTER TABLE `administrator` DISABLE KEYS */;
INSERT INTO `administrator` VALUES (1,5,3,'2025-04-18 09:29:05',NULL,0),(2,4,3,'2025-04-18 09:34:04',NULL,0);
/*!40000 ALTER TABLE `administrator` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `device`
--

DROP TABLE IF EXISTS `device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '设备ID',
  `exhibition_id` int NOT NULL COMMENT '所属展厅ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备名称',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '设备状态(0:离线,1:在线)',
  `identifier` varchar(100) NOT NULL COMMENT '设备标识',
  `device_type_id` int NOT NULL COMMENT '设备类型',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  `mac_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'mac地址',
  `enable_command` varchar(100) DEFAULT NULL COMMENT '设备开启指令',
  `disable_command` varchar(100) DEFAULT NULL COMMENT '设备关闭指令',
  `port` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '协议端口（udp等）',
  `protocol_type_id` int DEFAULT NULL COMMENT '协议类型',
  PRIMARY KEY (`id`),
  KEY `idx_exhibition_id` (`exhibition_id`),
  KEY `device_device_type_IDX` (`device_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物联网设备表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `device`
--

LOCK TABLES `device` WRITE;
/*!40000 ALTER TABLE `device` DISABLE KEYS */;
INSERT INTO `device` VALUES (1,3,'1号展厅电脑','***********',0,'1',1,'2025-04-18 15:44:18','2025-07-24 14:29:06',1,'',NULL,NULL,NULL,1),(2,3,'2号展厅电脑','192.168.1.150',1,'11',1,'2025-04-18 17:10:54','2025-07-24 14:29:08',1,'D8-5E-D3-23-3A-B2',NULL,NULL,NULL,1),(3,3,'3号展厅电脑','192.168.1.2',0,'123',1,'2025-04-18 17:39:08','2025-07-24 14:29:10',1,'',NULL,NULL,NULL,1),(4,3,'湿度计','192.168.1.4',0,'4',2,'2025-06-19 17:29:03','2025-07-24 14:29:13',1,NULL,NULL,NULL,NULL,1),(5,3,'滑轨拼接屏','192.168.1.42',0,'1',3,'2025-07-24 14:28:45','2025-07-24 14:33:51',0,NULL,NULL,NULL,NULL,1),(6,3,'手环投影','192.168.1.111',0,'1',3,'2025-07-24 14:38:04',NULL,0,NULL,NULL,NULL,NULL,1),(7,3,'大屏拼接','192.168.1.20',0,'1',3,'2025-07-24 14:39:03',NULL,0,NULL,NULL,NULL,NULL,1),(8,3,'三通道弧幕','***********8',0,'1',3,'2025-07-24 14:39:26','2025-07-26 15:34:06',0,NULL,NULL,NULL,NULL,1),(9,3,'翻书投影','***********9',0,'1',3,'2025-07-24 14:39:40','2025-07-26 15:34:01',0,NULL,NULL,NULL,NULL,1),(10,3,'答题投影','192.168.1.43',0,'1',3,'2025-07-24 14:39:52',NULL,0,NULL,NULL,NULL,NULL,1),(11,3,'灯光','192.168.1.95',0,'2',5,'2025-07-24 14:42:41',NULL,0,NULL,NULL,NULL,NULL,1),(12,3,'办公室电灯','*************',1,'1',4,'2025-07-24 15:32:33','2025-07-31 16:12:45',0,NULL,'55 01 11 00 00 00 01 68','55 01 12 00 00 00 01 69','20108',1);
/*!40000 ALTER TABLE `device` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `device_police_tag`
--

DROP TABLE IF EXISTS `device_police_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_police_tag` (
  `id` int NOT NULL AUTO_INCREMENT,
  `device_id` int NOT NULL COMMENT '设备id',
  `policy_id` int NOT NULL COMMENT '策略标签id',
  `create_time` date NOT NULL COMMENT '创建时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备策略标签关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `device_police_tag`
--

LOCK TABLES `device_police_tag` WRITE;
/*!40000 ALTER TABLE `device_police_tag` DISABLE KEYS */;
INSERT INTO `device_police_tag` VALUES (1,1,8,'2025-04-18',1),(5,4,10,'2025-06-19',1),(6,3,8,'2025-06-19',1),(7,3,9,'2025-06-19',1),(8,4,10,'2025-06-20',1),(9,4,10,'2025-06-20',1),(10,4,10,'2025-06-20',1),(11,3,8,'2025-06-20',1),(12,3,9,'2025-06-20',1),(13,4,10,'2025-06-20',1),(14,4,10,'2025-06-20',1),(15,3,8,'2025-06-20',1),(16,3,9,'2025-06-20',1),(17,4,10,'2025-06-20',1),(18,5,10,'2025-07-24',1),(19,5,10,'2025-07-24',0),(20,6,10,'2025-07-24',0);
/*!40000 ALTER TABLE `device_police_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `device_type`
--

DROP TABLE IF EXISTS `device_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_type` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `exhibition_id` int NOT NULL COMMENT '所属展厅ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型名称',
  `description` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0:未发布,1:已发布)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `device_type`
--

LOCK TABLES `device_type` WRITE;
/*!40000 ALTER TABLE `device_type` DISABLE KEYS */;
INSERT INTO `device_type` VALUES (1,3,'电脑','PC端主机设备',0,'2025-06-18 11:47:13','2025-07-24 14:32:35',1),(2,3,'温湿计','',1,'2025-06-19 16:05:14','2025-07-24 14:32:40',1),(3,3,'显示屏','',1,'2025-07-24 14:33:11','2025-07-24 14:33:25',0),(4,3,'主机','',1,'2025-07-24 14:33:19','2025-07-24 14:33:23',0),(5,3,'灯','',1,'2025-07-24 14:42:13','2025-07-24 14:42:17',0);
/*!40000 ALTER TABLE `device_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exhibition`
--

DROP TABLE IF EXISTS `exhibition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exhibition` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '展厅ID',
  `name` varchar(100) NOT NULL COMMENT '展厅名称',
  `address` varchar(255) NOT NULL COMMENT '项目地址',
  `description` text COMMENT '项目简介',
  `logo_path` varchar(255) DEFAULT NULL COMMENT 'Logo存储路径',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:启用,0:禁用)',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  `exhibition_ip` varchar(100) NOT NULL COMMENT '项目IP',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='展厅信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exhibition`
--

LOCK TABLES `exhibition` WRITE;
/*!40000 ALTER TABLE `exhibition` DISABLE KEYS */;
INSERT INTO `exhibition` VALUES (1,'展厅1','广东省深圳市南山区西丽街道','123','1745742718861_logo.png','2025-03-31 17:39:08',NULL,1,0,'************'),(2,'123213','123123','公司拥有自己近3000平米硬件定制、加工工厂，有近10年的硬件制造经验，包括自主结构设计、硬件定制、软件开发、生产装配等服务，为客户提供高效的产品定制开发及优质配套服务。振邦视界和中粮集团合作在深圳·宝安打造了近400平米自有实景科技展厅，集中展示了目前比较新锐的各类多媒体互动技术，欢迎各位合作伙伴前来考察体验！','1744872999687_logo.png','2025-04-17 14:52:42',NULL,1,0,'113231'),(3,'123123','123123','公司现有品类丰富、技术新锐、性能稳定的互动多媒体产品资源库，包括触摸互动、投影互动、雷达互动、智能中控、AR&VR互动、滑轨互动、动力矩阵、全息成像、沉浸式体验厅及多媒体沙盘控制技术等数十类互动系列产品。','1744872999687_logo.png','2025-04-17 14:54:04',NULL,1,0,'123123'),(4,'123123','宝安会展中心','振邦视界集多媒体互动方案设计、软件开发、硬件定制、安装调试、技术培训及售后服务于一体，致力于为客户提供一站式交付互动多媒体整体解决方案。基于不同项目的个性化服务需求，为政府、企业、地产等提供软硬件配套定制服务，不断积累企业展馆、党政展厅、博物馆、禁毒馆、青少年法制教育展馆等多类行业展馆展厅的多媒体解决方案。','1744872999687_logo.png','2025-04-17 14:56:40',NULL,1,0,'123123'),(5,'振邦视界','123','中控系统','1745742718861_logo.png','2025-04-27 16:31:59',NULL,1,0,'123');
/*!40000 ALTER TABLE `exhibition` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permission`
--

DROP TABLE IF EXISTS `permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `description` varchar(255) DEFAULT NULL COMMENT '权限描述',
  `code` varchar(50) NOT NULL COMMENT '权限代码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permission`
--

LOCK TABLES `permission` WRITE;
/*!40000 ALTER TABLE `permission` DISABLE KEYS */;
INSERT INTO `permission` VALUES (1,'展厅查看','查看展厅信息权限','EXHIBITION_VIEW'),(2,'展厅管理','管理展厅信息权限','EXHIBITION_MANAGE'),(3,'设备查看','查看设备信息权限','DEVICE_VIEW'),(4,'设备管理','管理设备信息权限','DEVICE_MANAGE'),(5,'定时任务查看','查看定时任务权限','SCHEDULE_VIEW'),(6,'定时任务管理','管理定时任务权限','SCHEDULE_MANAGE'),(7,'视频资源查看','查看视频资源权限','VIDEO_VIEW'),(8,'视频资源管理','管理视频资源权限','VIDEO_MANAGE'),(9,'管理员管理','管理管理员权限','ADMIN_MANAGE'),(10,'系统管理','系统管理权限','SYSTEM_MANAGE'),(11,'策略查看','查看策略信息权限','STRATEGY_VIEW'),(12,'策略管理','管理策略信息权限','STRATEGY_MANAGE');
/*!40000 ALTER TABLE `permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `policy_tag`
--

DROP TABLE IF EXISTS `policy_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `policy_tag` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '标签id',
  `exhibition_id` int NOT NULL COMMENT '所属展厅id',
  `tag_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称',
  `tag_description` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'tag_description',
  `status` tinyint DEFAULT NULL COMMENT '启用状态',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `policy_tag_tag_name_IDX` (`tag_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `policy_tag`
--

LOCK TABLES `policy_tag` WRITE;
/*!40000 ALTER TABLE `policy_tag` DISABLE KEYS */;
INSERT INTO `policy_tag` VALUES (8,3,'1','1',1,'2025-04-18 15:39:51','2025-04-18 15:40:00',0),(9,3,'12','123',1,'2025-04-18 15:40:23','2025-04-18 15:52:14',0),(10,3,'test','test',1,'2025-04-19 16:43:17','2025-04-21 17:39:13',0);
/*!40000 ALTER TABLE `policy_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `protocol_type`
--

DROP TABLE IF EXISTS `protocol_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `protocol_type` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '协议ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '协议名称',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='协议类型';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `protocol_type`
--

LOCK TABLES `protocol_type` WRITE;
/*!40000 ALTER TABLE `protocol_type` DISABLE KEYS */;
INSERT INTO `protocol_type` VALUES (1,'UDP协议','2025-07-25 14:56:07',0),(2,'LAN唤醒','2025-07-25 14:59:31',0);
/*!40000 ALTER TABLE `protocol_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `schedule`
--

DROP TABLE IF EXISTS `schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `schedule` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `device_id` int NOT NULL COMMENT '设备ID',
  `name` varchar(100) NOT NULL COMMENT '任务名称',
  `type` varchar(50) NOT NULL COMMENT '任务类型',
  `execution_time` datetime NOT NULL COMMENT '执行时间',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '任务状态(0:待执行,1:已执行,2:失败)',
  `content` json DEFAULT NULL COMMENT '任务内容(JSON格式)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定时任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `schedule`
--

LOCK TABLES `schedule` WRITE;
/*!40000 ALTER TABLE `schedule` DISABLE KEYS */;
/*!40000 ALTER TABLE `schedule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `strategy`
--

DROP TABLE IF EXISTS `strategy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `strategy` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '策略ID',
  `exhibition_id` int NOT NULL COMMENT '所属展厅ID',
  `name` varchar(100) NOT NULL COMMENT '策略名称',
  `type` tinyint(1) NOT NULL COMMENT '策略类型(1:群组,2:定时,3:联动)',
  `description` text COMMENT '策略描述',
  `config` json NOT NULL COMMENT '策略配置(JSON格式)',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0:禁用,1:启用)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `last_executed` datetime DEFAULT NULL COMMENT '最后执行时间',
  `execution_count` int NOT NULL DEFAULT '0' COMMENT '执行次数',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_exhibition_id` (`exhibition_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `strategy`
--

LOCK TABLES `strategy` WRITE;
/*!40000 ALTER TABLE `strategy` DISABLE KEYS */;
INSERT INTO `strategy` VALUES (1,3,'展厅灯光群组控制',1,'统一控制展厅所有灯光设备的开关','{\"devices\": [{\"id\": 1, \"name\": \"主展区灯光\", \"ipAddress\": \"*************\"}, {\"id\": 2, \"name\": \"入口灯光\", \"ipAddress\": \"*************\"}], \"groupName\": \"展厅灯光群组\", \"defaultAction\": \"power_on\"}',1,'2025-07-31 10:12:33','2025-07-31 10:54:06','2025-07-31 10:54:06',1,0),(2,3,'每日开馆定时策略',2,'每日上午9点自动开启所有展示设备','{\"action\": \"power_on\", \"timeout\": 30, \"weekdays\": [], \"retryCount\": 3, \"executeDate\": null, \"executeTime\": \"17:12:00\", \"scheduleType\": \"daily\", \"customCommand\": \"\", \"targetDevices\": [{\"id\": 12, \"name\": \"办公室电灯\", \"port\": 20108, \"status\": 1, \"deleted\": 0, \"ipAddress\": \"*************\", \"policyTag\": [], \"createTime\": \"2025-07-24T15:32:33\", \"identifier\": \"1\", \"macAddress\": null, \"updateTime\": \"2025-07-28T16:07:47\", \"deviceTypeId\": 4, \"exhibitionId\": 3, \"enableCommand\": \"55 01 11 00 00 00 01 68\", \"disableCommand\": \"55 01 12 00 00 00 01 69\", \"protocolTypeId\": 1}], \"retryOnFailure\": false}',1,'2025-07-31 10:12:33','2025-07-31 17:11:25','2025-07-31 17:11:25',17,0),(3,3,'温度异常联动策略',3,'当温度传感器检测到异常高温时，自动开启空调设备','{\"actions\": [{\"deviceId\": 6, \"actionType\": \"power_on\"}], \"triggerValue\": \"28\", \"triggerDeviceId\": 5, \"triggerCondition\": \"temperature_above\"}',0,'2025-07-31 10:12:33','2025-07-31 10:54:15',NULL,0,0);
/*!40000 ALTER TABLE `strategy` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `strategy_device`
--

DROP TABLE IF EXISTS `strategy_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `strategy_device` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `device_id` int NOT NULL COMMENT '设备ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_device` (`strategy_id`,`device_id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略设备关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `strategy_device`
--

LOCK TABLES `strategy_device` WRITE;
/*!40000 ALTER TABLE `strategy_device` DISABLE KEYS */;
/*!40000 ALTER TABLE `strategy_device` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `strategy_execution_log`
--

DROP TABLE IF EXISTS `strategy_execution_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `strategy_execution_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `execution_time` datetime NOT NULL COMMENT '执行时间',
  `status` tinyint(1) NOT NULL COMMENT '执行状态(0:失败,1:成功,2:部分成功)',
  `result` text COMMENT '执行结果详情',
  `error_message` text COMMENT '错误信息',
  `execution_duration` int DEFAULT NULL COMMENT '执行耗时(毫秒)',
  `affected_devices` json DEFAULT NULL COMMENT '影响的设备列表(JSON格式)',
  `trigger_type` varchar(50) DEFAULT NULL COMMENT '触发类型(manual:手动,scheduled:定时,linkage:联动)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_execution_time` (`execution_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略执行记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `strategy_execution_log`
--

LOCK TABLES `strategy_execution_log` WRITE;
/*!40000 ALTER TABLE `strategy_execution_log` DISABLE KEYS */;
INSERT INTO `strategy_execution_log` VALUES (1,1,'2025-07-31 10:54:06',1,'策略执行成功',NULL,220,'[\"主展区灯光\", \"入口灯光\"]','manual','2025-07-31 10:54:06'),(2,2,'2025-07-31 14:45:39',1,'策略执行成功',NULL,111,'[\"办公室电灯\"]','manual','2025-07-31 14:45:39'),(3,2,'2025-07-31 14:55:51',1,'策略执行成功',NULL,116,'[\"办公室电灯\"]','manual','2025-07-31 14:55:51'),(4,2,'2025-07-31 14:56:36',1,'策略执行成功',NULL,132,'[\"办公室电灯\"]','manual','2025-07-31 14:56:36'),(5,2,'2025-07-31 15:11:58',1,'策略执行成功',NULL,114,'[\"办公室电灯\"]','manual','2025-07-31 15:11:58'),(6,2,'2025-07-31 15:12:09',1,'策略执行成功',NULL,110,'[\"办公室电灯\"]','manual','2025-07-31 15:12:09'),(7,2,'2025-07-31 15:12:29',1,'策略执行成功',NULL,115,'[\"办公室电灯\"]','manual','2025-07-31 15:12:29'),(8,2,'2025-07-31 15:23:38',1,'策略执行成功',NULL,113,'[\"办公室电灯\"]','manual','2025-07-31 15:23:39'),(9,2,'2025-07-31 16:06:31',1,'策略执行成功',NULL,110,'[\"办公室电灯\"]','manual','2025-07-31 16:06:31'),(10,2,'2025-07-31 16:11:15',1,'策略执行成功',NULL,14,'[\"办公室电灯\"]','manual','2025-07-31 16:11:15'),(11,2,'2025-07-31 16:12:27',1,'策略执行成功',NULL,3,'[\"办公室电灯\"]','manual','2025-07-31 16:12:27'),(12,2,'2025-07-31 16:14:21',1,'策略执行成功',NULL,22,'[\"办公室电灯\"]','manual','2025-07-31 16:14:21'),(13,2,'2025-07-31 16:14:33',1,'策略执行成功',NULL,3,'[\"办公室电灯\"]','manual','2025-07-31 16:14:33'),(14,2,'2025-07-31 16:14:38',1,'策略执行成功',NULL,3,'[\"办公室电灯\"]','manual','2025-07-31 16:14:38'),(15,2,'2025-07-31 17:11:02',1,'策略执行成功',NULL,7,'[\"办公室电灯\"]','manual','2025-07-31 17:11:02'),(16,2,'2025-07-31 17:11:10',1,'策略执行成功',NULL,2,'[\"办公室电灯\"]','manual','2025-07-31 17:11:10'),(17,2,'2025-07-31 17:11:18',1,'策略执行成功',NULL,2,'[\"办公室电灯\"]','manual','2025-07-31 17:11:18'),(18,2,'2025-07-31 17:11:25',1,'策略执行成功',NULL,3,'[\"办公室电灯\"]','manual','2025-07-31 17:11:25');
/*!40000 ALTER TABLE `strategy_execution_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `strategy_linkage_action`
--

DROP TABLE IF EXISTS `strategy_linkage_action`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `strategy_linkage_action` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '动作ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `target_device_id` int NOT NULL COMMENT '目标设备ID',
  `action_type` varchar(50) NOT NULL COMMENT '动作类型',
  `action_value` varchar(255) DEFAULT NULL COMMENT '动作值',
  `delay_seconds` int NOT NULL DEFAULT '0' COMMENT '延迟执行秒数',
  `order_index` int NOT NULL DEFAULT '0' COMMENT '执行顺序',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_target_device_id` (`target_device_id`),
  KEY `idx_order_index` (`order_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略联动动作表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `strategy_linkage_action`
--

LOCK TABLES `strategy_linkage_action` WRITE;
/*!40000 ALTER TABLE `strategy_linkage_action` DISABLE KEYS */;
/*!40000 ALTER TABLE `strategy_linkage_action` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `strategy_linkage_condition`
--

DROP TABLE IF EXISTS `strategy_linkage_condition`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `strategy_linkage_condition` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '条件ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `trigger_device_id` int NOT NULL COMMENT '触发设备ID',
  `condition_type` varchar(50) NOT NULL COMMENT '条件类型',
  `condition_value` varchar(255) DEFAULT NULL COMMENT '条件值',
  `operator` varchar(20) DEFAULT NULL COMMENT '操作符(>,<,=,!=等)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_trigger_device_id` (`trigger_device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略联动条件表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `strategy_linkage_condition`
--

LOCK TABLES `strategy_linkage_condition` WRITE;
/*!40000 ALTER TABLE `strategy_linkage_condition` DISABLE KEYS */;
/*!40000 ALTER TABLE `strategy_linkage_condition` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `strategy_schedule`
--

DROP TABLE IF EXISTS `strategy_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `strategy_schedule` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `strategy_id` int NOT NULL COMMENT '策略ID',
  `cron_expression` varchar(100) DEFAULT NULL COMMENT 'Cron表达式',
  `next_execution_time` datetime DEFAULT NULL COMMENT '下次执行时间',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_id` (`strategy_id`),
  KEY `idx_next_execution_time` (`next_execution_time`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='策略定时任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `strategy_schedule`
--

LOCK TABLES `strategy_schedule` WRITE;
/*!40000 ALTER TABLE `strategy_schedule` DISABLE KEYS */;
/*!40000 ALTER TABLE `strategy_schedule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户昵称',
  `password` varchar(255) NOT NULL COMMENT '密码(加密存储)',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `register_time` datetime NOT NULL COMMENT '注册时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `invitation_code` varchar(20) DEFAULT NULL COMMENT '注册时使用的邀请码',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '账号状态(1:正常,0:禁用)',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  `avatar_path` varchar(100) DEFAULT NULL COMMENT '头像路径',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_phone` (`phone`),
  UNIQUE KEY `idx_email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (4,'admin','$2a$10$pzMtsUOoVjj1IRyD5bZCfulPNdQxuS.AlDiiQghNWhgCweHVCLgR2',NULL,'<EMAIL>','2025-04-16 15:04:54','2025-06-19 16:28:03',NULL,1,0,NULL),(5,'test','$2a$10$mMS2BZ4dosQscwN7XP22WeduDVURkH3tOC/sufGG9LjwBt2z/bhGy',NULL,'<EMAIL>','2025-04-17 17:42:23','2025-04-17 17:42:23',NULL,1,0,NULL);
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `video`
--

DROP TABLE IF EXISTS `video`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `video` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '视频ID',
  `exhibition_id` int NOT NULL COMMENT '所属展厅ID',
  `name` varchar(100) NOT NULL COMMENT '视频名称',
  `file_path` varchar(255) NOT NULL COMMENT '视频文件路径',
  `duration` int DEFAULT NULL COMMENT '视频时长(秒)',
  `upload_time` datetime NOT NULL COMMENT '上传时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:可用,0:不可用)',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_exhibition_id` (`exhibition_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='视频资源表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `video`
--

LOCK TABLES `video` WRITE;
/*!40000 ALTER TABLE `video` DISABLE KEYS */;
/*!40000 ALTER TABLE `video` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'exhibition_system'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-01 11:37:25
