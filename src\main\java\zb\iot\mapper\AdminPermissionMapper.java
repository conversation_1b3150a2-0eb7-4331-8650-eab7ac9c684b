package zb.iot.mapper;

import org.apache.ibatis.annotations.Param;
import zb.iot.entity.AdminPermission;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 管理员权限关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface AdminPermissionMapper extends BaseMapper<AdminPermission> {

    /**
     * 批量插入管理员权限关联
     */
    int batchInsert(@Param("list") List<AdminPermission> adminPermissions);

    /**
     * 根据管理员ID删除所有权限关联
     */
    int deleteByAdminId(@Param("adminId") Integer adminId);
}
