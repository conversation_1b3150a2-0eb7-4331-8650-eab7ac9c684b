package zb.iot.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import zb.iot.config.UdpConfig;
import zb.iot.entity.Device;
import zb.iot.mapper.DeviceMapper;
import zb.iot.service.IUdpService;
import zb.iot.utils.UdpUtil;

import java.net.DatagramPacket;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * UDP服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UdpServiceImpl implements IUdpService {

    private final UdpConfig udpConfig;
    private final DeviceMapper deviceMapper;
    
    // 存储发现的设备信息
    private final CopyOnWriteArrayList<String> discoveredDevices = new CopyOnWriteArrayList<>();

    @Override
    public boolean sendMessage(String host, int port, String message) {
        byte[] data = message.getBytes(StandardCharsets.UTF_8);
        return UdpUtil.sendPacket(host, port, data);
    }
    
    @Override
    public boolean sendMessage(String host, int port, String message, Charset charset) {
        return UdpUtil.sendPacket(host, port, message, charset);
    }
    
    @Override
    public boolean sendHexMessage(String host, int port, String hexString) {
        return UdpUtil.sendHexPacket(host, port, hexString);
    }

    @Override
    public boolean sendData(String host, int port, byte[] data) {
        return UdpUtil.sendPacket(host, port, data);
    }

    @Override
    public boolean broadcastMessage(int port, String message) {
        byte[] data = message.getBytes(StandardCharsets.UTF_8);
        return UdpUtil.broadcastPacket(port, data);
    }
    
    @Override
    public boolean broadcastMessage(int port, String message, Charset charset) {
        return UdpUtil.broadcastPacket(port, message, charset);
    }
    
    @Override
    public boolean broadcastHexMessage(int port, String hexString) {
        return UdpUtil.broadcastHexPacket(port, hexString);
    }

    @Override
    public boolean multicastMessage(String multicastAddress, int port, String message) {
        byte[] data = message.getBytes(StandardCharsets.UTF_8);
        return UdpUtil.multicastPacket(multicastAddress, port, data, udpConfig.getMulticastTtl());
    }
    
    @Override
    public boolean multicastMessage(String multicastAddress, int port, String message, Charset charset) {
        return UdpUtil.multicastPacket(multicastAddress, port, message, charset, udpConfig.getMulticastTtl());
    }
    
    @Override
    public boolean multicastHexMessage(String multicastAddress, int port, String hexString) {
        return UdpUtil.multicastHexPacket(multicastAddress, port, hexString, udpConfig.getMulticastTtl());
    }

    @Override
    public String receiveMessage(int port, int timeout) {
        byte[] data = UdpUtil.receivePacket(port, timeout);
        if (data != null) {
            return new String(data, StandardCharsets.UTF_8);
        }
        return null;
    }
    
    @Override
    public String receiveMessage(int port, int timeout, Charset charset) {
        return UdpUtil.receiveMessage(port, timeout, charset);
    }
    
    @Override
    public String receiveHexMessage(int port, int timeout) {
        return UdpUtil.receiveHexString(port, timeout);
    }

    @Override
    public boolean startListener(int port, Consumer<String> messageHandler) {
        return UdpUtil.startMessageListener(port, messageHandler, StandardCharsets.UTF_8);
    }
    
    @Override
    public boolean startListener(int port, Consumer<String> messageHandler, Charset charset) {
        return UdpUtil.startMessageListener(port, messageHandler, charset);
    }
    
    @Override
    public boolean startHexListener(int port, Consumer<String> hexHandler) {
        return UdpUtil.startHexListener(port, hexHandler);
    }

    @Override
    public boolean startPacketListener(int port, Consumer<DatagramPacket> packetHandler) {
        return UdpUtil.startListener(port, packetHandler);
    }

    @Override
    public boolean stopListener(int port) {
        return UdpUtil.stopListener(port);
    }

    @Override
    public void stopAllListeners() {
        UdpUtil.stopAllListeners();
    }

    @Override
    public boolean sendDeviceCommand(String deviceIp, int port, String command) {
        log.info("发送UDP命令到设备: IP={}, 端口={}, 命令={}", deviceIp, port, command);
        return sendMessage(deviceIp, port, command);
    }
    
    @Override
    public boolean sendDeviceHexCommand(String deviceIp, int port, String hexCommand) {
        log.info("发送16进制UDP命令到设备: IP={}, 端口={}, 命令={}", deviceIp, port, hexCommand);
        return sendHexMessage(deviceIp, port, hexCommand);
    }

    @Override
    public int discoverDevices(int discoveryPort, int timeout) {
        // 清空上次发现的设备列表
        discoveredDevices.clear();
        
        // 创建CountDownLatch用于等待设备发现过程完成
        CountDownLatch latch = new CountDownLatch(1);
        
        // 启动监听器接收设备响应
        boolean listenerStarted = startPacketListener(discoveryPort, packet -> {
            try {
                String response = new String(UdpUtil.extractData(packet), StandardCharsets.UTF_8);
                String deviceAddress = packet.getAddress().getHostAddress();
                
                // 假设设备响应格式为: DEVICE_INFO:设备标识符:设备类型
                if (response.startsWith("DEVICE_INFO:")) {
                    String[] parts = response.split(":", 3);
                    if (parts.length >= 3) {
                        String deviceId = parts[1];
                        String deviceType = parts[2];
                        
                        String deviceInfo = String.format("%s:%s:%s", deviceAddress, deviceId, deviceType);
                        if (!discoveredDevices.contains(deviceInfo)) {
                            discoveredDevices.add(deviceInfo);
                            log.info("发现设备: IP={}, 标识符={}, 类型={}", deviceAddress, deviceId, deviceType);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析设备发现响应异常: {}", e.getMessage(), e);
            }
        });
        
        if (!listenerStarted) {
            log.error("启动设备发现监听器失败");
            return 0;
        }
        
        // 广播设备发现请求
        log.info("广播设备发现请求到端口: {}", discoveryPort);
        boolean sent = broadcastMessage(discoveryPort, "DISCOVER_DEVICES");
        if (!sent) {
            stopListener(discoveryPort);
            return 0;
        }
        
        // 等待设备响应
        try {
            latch.await(timeout, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("设备发现过程被中断");
        } finally {
            stopListener(discoveryPort);
        }
        
        return discoveredDevices.size();
    }
    
    @Override
    public String bytesToHexString(byte[] bytes) {
        return UdpUtil.byteArrayToHexString(bytes);
    }
    
    @Override
    public byte[] hexStringToBytes(String hexString) throws IllegalArgumentException {
        return UdpUtil.hexStringToByteArray(hexString);
    }

    @Override
    public boolean testConnection(String host, int port) {
        try {
            // 发送测试消息并等待响应
            String testMessage = "PING";
            boolean sent = sendMessage(host, port, testMessage);
            if (!sent) {
                return false;
            }

            // 简单的连接测试，如果发送成功就认为连接正常
            // 实际应用中可能需要等待响应
            return true;
        } catch (Exception e) {
            log.error("测试UDP连接失败: {}:{}", host, port, e);
            return false;
        }
    }

    @Override
    public boolean pingHost(String host) {
        try {
            // 使用Java的InetAddress进行ping测试
            java.net.InetAddress address = java.net.InetAddress.getByName(host);
            return address.isReachable(3000); // 3秒超时
        } catch (Exception e) {
            log.error("Ping主机失败: {}", host, e);
            return false;
        }
    }

    @Override
    public boolean isPortOpen(String host, int port, int timeout) {
        try {
            // 尝试创建UDP连接测试端口
            java.net.Socket socket = new java.net.Socket();
            socket.connect(new java.net.InetSocketAddress(host, port), timeout);
            socket.close();
            return true;
        } catch (Exception e) {
            log.debug("端口不可达: {}:{}", host, port);
            return false;
        }
    }
}
