package zb.iot.entity.VO;

import lombok.Getter;
import lombok.Setter;
import zb.iot.entity.DeviceType;
import zb.iot.entity.PolicyTag;
import zb.iot.entity.ProtocolType;

import java.time.LocalDateTime;
import java.util.List;


@Setter
@Getter
public class DeviceVO {

    /**
     * 设备ID
     */
    private Integer id;

    /**
     * 所属展厅ID
     */
    private Integer exhibitionId;

    /**
     * 设备名称
     */
    private String name;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * MAC地址
     */
    private String macAddress;

    /**
     * 设备状态(0:离线,1:在线)
     */
    private Byte status;

    /**
     * 设备标识
     */
    private String identifier;

    /**
     * 设备类型
     */
    private DeviceType deviceType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 设备关联的权限列表 - 非数据库字段
     * 用于传输数据，不直接映射到数据库
     */
    private List<PolicyTag> policyTag;

    private ProtocolType protocolType;

    private Integer port;

    private String enableCommand;

    private String disableCommand;
}
