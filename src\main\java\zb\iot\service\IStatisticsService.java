package zb.iot.service;

import java.util.Map;

/**
 * 统计服务接口
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface IStatisticsService {

    /**
     * 获取设备类型统计数据
     * @param exhibitionId 展厅ID
     * @return 设备类型统计数据
     */
    Map<String, Object> getDeviceTypeStatistics(Integer exhibitionId);

    /**
     * 获取设备统计数据
     * @param exhibitionId 展厅ID
     * @return 设备统计数据
     */
    Map<String, Object> getDeviceStatistics(Integer exhibitionId);

    /**
     * 获取告警统计数据
     * @param exhibitionId 展厅ID
     * @return 告警统计数据
     */
    Map<String, Object> getAlarmStatistics(Integer exhibitionId);

    /**
     * 获取告警详细统计数据（用于图表）
     * @param exhibitionId 展厅ID
     * @return 告警详细统计数据
     */
    Map<String, Object> getAlarmDetailStatistics(Integer exhibitionId);
}
