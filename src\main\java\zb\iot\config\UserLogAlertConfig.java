package zb.iot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 用户日志告警配置类
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.user-log.alert")
public class UserLogAlertConfig {

    /**
     * 是否启用告警
     */
    private Boolean enabled = true;

    /**
     * 登录失败次数阈值（时间窗口内）
     */
    private Integer loginFailureThreshold = 5;

    /**
     * 时间窗口（分钟）
     */
    private Integer timeWindowMinutes = 15;

    /**
     * 异常IP检测阈值（时间窗口内的操作次数）
     */
    private Integer suspiciousIpThreshold = 100;

    /**
     * 错误操作阈值
     */
    private Integer errorOperationThreshold = 10;

    /**
     * 告警邮件接收者
     */
    private List<String> alertEmails;

    /**
     * 告警冷却时间（分钟）
     */
    private Integer cooldownMinutes = 60;
}
