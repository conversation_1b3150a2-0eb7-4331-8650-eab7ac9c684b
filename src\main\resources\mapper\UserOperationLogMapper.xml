<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zb.iot.mapper.UserOperationLogMapper">

    <!-- 分页查询用户操作日志 -->
    <select id="selectUserLogPage" resultType="zb.iot.entity.VO.UserOperationLogVO">
        SELECT 
            id,
            user_id as userId,
            username,
            operation_type as operationType,
            CASE operation_type
                WHEN 'LOGIN' THEN '登录'
                WHEN 'LOGOUT' THEN '登出'
                WHEN 'CREATE' THEN '创建'
                WHEN 'UPDATE' THEN '更新'
                WHEN 'DELETE' THEN '删除'
                WHEN 'EXECUTE' THEN '执行'
                WHEN 'VIEW' THEN '查看'
                WHEN 'EXPORT' THEN '导出'
                WHEN 'IMPORT' THEN '导入'
                ELSE operation_type
            END as operationTypeName,
            operation_module as operationModule,
            CASE operation_module
                WHEN 'STRATEGY' THEN '策略管理'
                WHEN 'DEVICE' THEN '设备管理'
                WHEN 'USER' THEN '用户管理'
                WHEN 'EXHIBITION' THEN '展厅管理'
                WHEN 'SYSTEM' THEN '系统管理'
                WHEN 'AUTH' THEN '认证授权'
                ELSE operation_module
            END as operationModuleName,
            operation_detail as operationDetail,
            target_id as targetId,
            target_name as targetName,
            ip_address as ipAddress,
            user_agent as userAgent,
            request_url as requestUrl,
            request_method as requestMethod,
            request_params as requestParams,
            response_status as responseStatus,
            CASE response_status
                WHEN 'SUCCESS' THEN '成功'
                WHEN 'FAILED' THEN '失败'
                WHEN 'ERROR' THEN '错误'
                ELSE response_status
            END as responseStatusName,
            error_message as errorMessage,
            execution_time as executionTime,
            exhibition_id as exhibitionId,
            create_time as createTime,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as createTimeFormatted
        FROM user_operation_log
        WHERE 1=1
        <if test="query.userId != null">
            AND user_id = #{query.userId}
        </if>
        <if test="query.username != null and query.username != ''">
            AND username LIKE CONCAT('%', #{query.username}, '%')
        </if>
        <if test="query.operationType != null and query.operationType != ''">
            AND operation_type = #{query.operationType}
        </if>
        <if test="query.operationModule != null and query.operationModule != ''">
            AND operation_module = #{query.operationModule}
        </if>
        <if test="query.ipAddress != null and query.ipAddress != ''">
            AND ip_address LIKE CONCAT('%', #{query.ipAddress}, '%')
        </if>
        <if test="query.responseStatus != null and query.responseStatus != ''">
            AND response_status = #{query.responseStatus}
        </if>
        <if test="query.exhibitionId != null">
            AND exhibition_id = #{query.exhibitionId}
        </if>
        <if test="query.startTime != null">
            AND create_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND create_time &lt;= #{query.endTime}
        </if>
        <if test="query.keyword != null and query.keyword != ''">
            AND (
                username LIKE CONCAT('%', #{query.keyword}, '%')
                OR operation_detail LIKE CONCAT('%', #{query.keyword}, '%')
                OR target_name LIKE CONCAT('%', #{query.keyword}, '%')
            )
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询用户最近的操作记录 -->
    <select id="selectRecentUserLogs" resultType="zb.iot.entity.VO.UserOperationLogVO">
        SELECT 
            id,
            user_id as userId,
            username,
            operation_type as operationType,
            operation_module as operationModule,
            operation_detail as operationDetail,
            target_name as targetName,
            response_status as responseStatus,
            create_time as createTime,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as createTimeFormatted
        FROM user_operation_log
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计用户操作情况 -->
    <select id="selectUserOperationStatistics" resultType="java.util.Map">
        SELECT 
            operation_type as operationType,
            COUNT(*) as count,
            AVG(execution_time) as avgExecutionTime
        FROM user_operation_log
        WHERE user_id = #{userId}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY operation_type
        ORDER BY count DESC
    </select>

    <!-- 统计操作类型分布 -->
    <select id="selectOperationTypeStatistics" resultType="java.util.Map">
        SELECT 
            operation_type as name,
            COUNT(*) as value
        FROM user_operation_log
        WHERE 1=1
        <if test="exhibitionId != null">
            AND exhibition_id = #{exhibitionId}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY operation_type
        ORDER BY value DESC
    </select>

    <!-- 统计操作模块分布 -->
    <select id="selectOperationModuleStatistics" resultType="java.util.Map">
        SELECT 
            operation_module as name,
            COUNT(*) as value
        FROM user_operation_log
        WHERE 1=1
        <if test="exhibitionId != null">
            AND exhibition_id = #{exhibitionId}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY operation_module
        ORDER BY value DESC
    </select>

    <!-- 统计每日操作量 -->
    <select id="selectDailyOperationStatistics" resultType="java.util.Map">
        SELECT 
            DATE(create_time) as date,
            COUNT(*) as count
        FROM user_operation_log
        WHERE 1=1
        <if test="exhibitionId != null">
            AND exhibition_id = #{exhibitionId}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        GROUP BY DATE(create_time)
        ORDER BY date ASC
    </select>

    <!-- 清理过期的操作日志 -->
    <delete id="deleteExpiredLogs">
        DELETE FROM user_operation_log
        WHERE create_time &lt; #{beforeTime}
    </delete>

    <!-- 批量清理过期的操作日志 -->
    <delete id="deleteExpiredLogsBatch">
        DELETE FROM user_operation_log
        WHERE create_time &lt; #{beforeTime}
        LIMIT #{batchSize}
    </delete>

    <!-- 批量插入日志记录 -->
    <insert id="batchInsert">
        INSERT INTO user_operation_log (
            user_id, username, operation_type, operation_module, operation_detail,
            target_id, target_name, ip_address, user_agent, request_url,
            request_method, request_params, response_status, error_message,
            execution_time, exhibition_id, create_time
        ) VALUES
        <foreach collection="logs" item="log" separator=",">
            (
                #{log.userId}, #{log.username}, #{log.operationType}, #{log.operationModule}, #{log.operationDetail},
                #{log.targetId}, #{log.targetName}, #{log.ipAddress}, #{log.userAgent}, #{log.requestUrl},
                #{log.requestMethod}, #{log.requestParams}, #{log.responseStatus}, #{log.errorMessage},
                #{log.executionTime}, #{log.exhibitionId}, #{log.createTime}
            )
        </foreach>
    </insert>

</mapper>
