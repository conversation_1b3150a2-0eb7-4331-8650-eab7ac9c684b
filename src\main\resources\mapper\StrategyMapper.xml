<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zb.iot.mapper.StrategyMapper">

    <!-- 策略VO结果映射 -->
    <resultMap id="StrategyVOMap" type="zb.iot.entity.VO.StrategyVO">
        <id column="id" property="id"/>
        <result column="exhibition_id" property="exhibitionId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="type_name" property="typeName"/>
        <result column="description" property="description"/>
        <result column="config" property="config"/>
        <result column="status" property="status"/>
        <result column="status_name" property="statusName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_executed" property="lastExecuted"/>
        <result column="execution_count" property="executionCount"/>
        <result column="executable" property="executable"/>
        <result column="last_execution_result" property="lastExecutionResult"/>
    </resultMap>

    <!-- 分页查询策略列表 -->
    <select id="selectStrategyPage" resultMap="StrategyVOMap">
        SELECT 
            s.id,
            s.exhibition_id,
            s.name,
            s.type,
            CASE s.type 
                WHEN 1 THEN '群组策略'
                WHEN 2 THEN '定时策略'
                WHEN 3 THEN '联动策略'
                ELSE '未知类型'
            END as type_name,
            s.description,
            s.config,
            s.status,
            CASE s.status 
                WHEN 1 THEN '启用'
                WHEN 0 THEN '禁用'
                ELSE '未知状态'
            END as status_name,
            s.create_time,
            s.update_time,
            s.last_executed,
            s.execution_count,
            (s.status = 1) as executable,
            (SELECT result FROM strategy_execution_log WHERE strategy_id = s.id ORDER BY execution_time DESC LIMIT 1) as last_execution_result
        FROM strategy s
        WHERE s.deleted = 0
        <if test="query.exhibitionId != null">
            AND s.exhibition_id = #{query.exhibitionId}
        </if>
        <if test="query.name != null and query.name != ''">
            AND s.name LIKE CONCAT('%', #{query.name}, '%')
        </if>
        <if test="query.type != null">
            AND s.type = #{query.type}
        </if>
        <if test="query.status != null">
            AND s.status = #{query.status}
        </if>
        ORDER BY s.create_time DESC
    </select>

    <!-- 根据ID查询策略详情 -->
    <select id="selectStrategyById" resultMap="StrategyVOMap">
        SELECT 
            s.id,
            s.exhibition_id,
            s.name,
            s.type,
            CASE s.type 
                WHEN 1 THEN '群组策略'
                WHEN 2 THEN '定时策略'
                WHEN 3 THEN '联动策略'
                ELSE '未知类型'
            END as type_name,
            s.description,
            s.config,
            s.status,
            CASE s.status 
                WHEN 1 THEN '启用'
                WHEN 0 THEN '禁用'
                ELSE '未知状态'
            END as status_name,
            s.create_time,
            s.update_time,
            s.last_executed,
            s.execution_count,
            (s.status = 1) as executable
        FROM strategy s
        WHERE s.id = #{id} AND s.deleted = 0
    </select>

    <!-- 查询指定展厅的所有启用策略 -->
    <select id="selectEnabledStrategiesByExhibitionId" resultType="zb.iot.entity.Strategy">
        SELECT * FROM strategy 
        WHERE exhibition_id = #{exhibitionId} 
        AND status = 1 
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询指定类型的启用策略 -->
    <select id="selectEnabledStrategiesByType" resultType="zb.iot.entity.Strategy">
        SELECT * FROM strategy 
        WHERE exhibition_id = #{exhibitionId} 
        AND type = #{type}
        AND status = 1 
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 更新策略最后执行时间和执行次数 -->
    <update id="updateLastExecuted">
        UPDATE strategy 
        SET last_executed = NOW(),
            execution_count = execution_count + 1,
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 查询需要执行的定时策略 -->
    <select id="selectScheduledStrategies" resultType="zb.iot.entity.Strategy">
        SELECT s.* FROM strategy s
        INNER JOIN strategy_schedule ss ON s.id = ss.strategy_id
        WHERE s.type = 2 
        AND s.status = 1 
        AND s.deleted = 0
        AND ss.is_active = 1
        AND ss.next_execution_time &lt;= NOW()
    </select>

</mapper>
