package zb.iot.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zb.iot.annotation.UserLog;
import zb.iot.common.ResponseResult;
import zb.iot.controller.dto.AuthResponse;
import zb.iot.controller.dto.LoginRequest;
import zb.iot.controller.dto.RegisterRequest;
import zb.iot.controller.dto.WechatLoginRequest;
import zb.iot.service.IAuthService;

/**
 * 登录登出验证
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final IAuthService authService;

    /**
     * 登录
     * @param request
     * @return
     */
    @PostMapping("/login")
    @UserLog(operationType = "LOGIN", operationModule = "AUTH", description = "用户登录")
    public ResponseResult<AuthResponse> login(@Valid @RequestBody LoginRequest request) {
        AuthResponse response = authService.login(request);
        return ResponseResult.success("登录成功", response);
    }

    /**
     * 注册
     * @param request
     * @return
     */
    @PostMapping("/register")
    @UserLog(operationType = "CREATE", operationModule = "AUTH", description = "用户注册")
    public ResponseResult<String> register(@Valid @RequestBody RegisterRequest request) {
        try {
            authService.register(request);
            return ResponseResult.success("注册成功");
        }catch (Exception e){
            return ResponseResult.fail(e.getMessage());
        }
    }

    /**
     * 微信登录
     * @param request
     * @return
     */
    @PostMapping("/wechat/login")
    @UserLog(operationType = "LOGIN", operationModule = "AUTH", description = "微信登录")
    public ResponseResult<AuthResponse> wechatLogin(@Valid @RequestBody WechatLoginRequest request) {
        AuthResponse response = authService.wechatLogin(request);
        return ResponseResult.success("微信登录成功", response);
    }

} 