{"openapi": "3.0.1", "info": {"title": "API Documentation", "version": "1.0"}, "paths": {"/auth/login": {"post": {"summary": "登录", "description": "登录", "requestBody": {"content": {"application/json": {"schema": {"required": ["password", "userIdentifier"], "type": "object", "properties": {"userIdentifier": {"type": "string", "description": "可以是用户名、邮箱或手机号"}, "password": {"type": "string", "description": ""}}, "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"token": {"type": "string", "description": ""}, "userId": {"type": "integer", "description": ""}, "permissions": {"type": "array", "description": "", "items": {"type": "integer"}}}, "description": "数据"}}, "description": ""}}}}}}}, "/auth/register": {"post": {"summary": "注册", "description": "注册", "requestBody": {"content": {"application/json": {"schema": {"required": ["password", "username"], "type": "object", "properties": {"username": {"maxLength": 20, "minLength": 3, "type": "string", "description": ""}, "password": {"maxLength": 20, "minLength": 6, "type": "string", "description": ""}, "phone": {"pattern": "^1[3-9]\\d{9}$", "type": "string", "description": ""}, "email": {"type": "string", "description": "", "format": "email"}, "invitationCode": {"type": "string", "description": ""}}, "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": ""}}}}}}}, "/auth/wechat/login": {"post": {"summary": "微信登录", "description": "微信登录", "requestBody": {"content": {"application/json": {"schema": {"required": ["code"], "type": "object", "properties": {"code": {"type": "string", "description": ""}}, "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"token": {"type": "string", "description": ""}, "userId": {"type": "integer", "description": ""}, "permissions": {"type": "array", "description": "", "items": {"type": "integer"}}}, "description": "数据"}}, "description": ""}}}}}}}, "/auth/sendCode": {"get": {"summary": "sendEmailCode", "description": "sendEmailCode", "parameters": [{"name": "email", "in": "query", "description": "", "required": true, "schema": {"type": "string", "nullable": false}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}}}}}}}}, "/auth/verifyCode": {"post": {"summary": "verifyEmailCode", "description": "verifyEmailCode", "parameters": [{"name": "email", "in": "query", "description": "", "required": true, "schema": {"type": "string", "nullable": false}}, {"name": "code", "in": "query", "description": "", "required": true, "schema": {"type": "string", "nullable": false}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}}}}}}}}, "/devices/add": {"post": {"summary": "添加设备", "description": "添加设备", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "设备ID"}, "exhibitionId": {"type": "integer", "description": "所属展厅ID"}, "name": {"type": "string", "description": "设备名称"}, "ipAddress": {"type": "string", "description": "IP地址"}, "macAddress": {"type": "string", "description": "MAC地址"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "设备状态(0:离线,1:在线)"}, "identifier": {"type": "string", "description": "设备标识"}, "deviceType": {"type": "integer", "description": "设备类型"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}, "policyTag": {"type": "array", "description": "设备关联的权限列表 - 非数据库字段\n用于传输数据，不直接映射到数据库", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键"}, "exhibitionId": {"type": "integer", "description": "展厅id"}, "tagName": {"type": "string", "description": "标签名称"}, "tagDescription": {"type": "string", "description": "标签描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "启用状态"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}}, "description": "设备策略标签表"}}}, "description": "设备信息（包含名称、IP、标识和策略标签列表）"}}}}, "responses": {"200": {"description": "添加结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "添加结果"}}}}}}}, "/devices/update": {"post": {"summary": "更新设备", "description": "更新设备", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "设备ID"}, "exhibitionId": {"type": "integer", "description": "所属展厅ID"}, "name": {"type": "string", "description": "设备名称"}, "ipAddress": {"type": "string", "description": "IP地址"}, "macAddress": {"type": "string", "description": "MAC地址"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "设备状态(0:离线,1:在线)"}, "identifier": {"type": "string", "description": "设备标识"}, "deviceType": {"type": "integer", "description": "设备类型"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}, "policyTag": {"type": "array", "description": "设备关联的权限列表 - 非数据库字段\n用于传输数据，不直接映射到数据库", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键"}, "exhibitionId": {"type": "integer", "description": "展厅id"}, "tagName": {"type": "string", "description": "标签名称"}, "tagDescription": {"type": "string", "description": "标签描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "启用状态"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}}, "description": "设备策略标签表"}}}, "description": "设备信息（包含名称、IP、状态、标识和策略标签列表）"}}}}, "responses": {"200": {"description": "更新结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "更新结果"}}}}}}}, "/devices/del/{id}": {"delete": {"summary": "删除设备", "description": "删除设备", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "删除结果"}}}}}}}, "/devices/getDeviceDetail/{id}": {"get": {"summary": "获取设备详情", "description": "获取设备详情", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "设备详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "设备ID"}, "exhibitionId": {"type": "integer", "description": "所属展厅ID"}, "name": {"type": "string", "description": "设备名称"}, "ipAddress": {"type": "string", "description": "IP地址"}, "macAddress": {"type": "string", "description": "MAC地址"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "设备状态(0:离线,1:在线)"}, "identifier": {"type": "string", "description": "设备标识"}, "deviceType": {"type": "integer", "description": "设备类型"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}, "policyTag": {"type": "array", "description": "设备关联的权限列表 - 非数据库字段\n用于传输数据，不直接映射到数据库", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键"}, "exhibitionId": {"type": "integer", "description": "展厅id"}, "tagName": {"type": "string", "description": "标签名称"}, "tagDescription": {"type": "string", "description": "标签描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "启用状态"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}}, "description": "设备策略标签表"}}}, "description": "数据"}}, "description": "设备详情"}}}}}}}, "/devices/page/{exhibitionId}": {"get": {"summary": "分页查询设备", "description": "分页查询设备", "parameters": [{"name": "exhibitionId", "in": "path", "description": "", "required": true, "schema": {"type": "integer"}}, {"name": "pageNum", "in": "query", "description": "页码", "required": true, "schema": {"type": "integer", "nullable": false, "default": "1"}}, {"name": "pageSize", "in": "query", "description": "每页大小", "required": true, "schema": {"type": "integer", "nullable": false, "default": "10"}}, {"name": "name", "in": "query", "description": "设备名称（可选，用于模糊查询）", "required": false, "schema": {"type": "string", "nullable": false}}], "responses": {"200": {"description": "分页设备列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"records": {"type": "array", "description": "", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "设备ID"}, "exhibitionId": {"type": "integer", "description": "所属展厅ID"}, "name": {"type": "string", "description": "设备名称"}, "ipAddress": {"type": "string", "description": "IP地址"}, "macAddress": {"type": "string", "description": "MAC地址"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "设备状态(0:离线,1:在线)"}, "identifier": {"type": "string", "description": "设备标识"}, "deviceType": {"type": "integer", "description": "设备类型"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}, "policyTag": {"type": "array", "description": "设备关联的权限列表 - 非数据库字段\n用于传输数据，不直接映射到数据库", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键"}, "exhibitionId": {"type": "integer", "description": "展厅id"}, "tagName": {"type": "string", "description": "标签名称"}, "tagDescription": {"type": "string", "description": "标签描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "启用状态"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}}, "description": "设备策略标签表"}}}, "description": "物联网设备表"}}, "total": {"type": "integer", "description": "", "format": "int64"}, "size": {"type": "integer", "description": "", "format": "int64"}, "current": {"type": "integer", "description": "", "format": "int64"}, "orders": {"type": "array", "description": "", "items": {"type": "object", "properties": {"column": {"type": "string", "description": ""}, "asc": {"type": "boolean", "description": ""}}, "description": "com.baomidou.mybatisplus.core.metadata.OrderItem"}}, "optimizeCountSql": {"type": "boolean", "description": ""}, "searchCount": {"type": "boolean", "description": ""}, "optimizeJoinOfCountSql": {"type": "boolean", "description": ""}, "maxLimit": {"type": "integer", "description": "", "format": "int64"}, "countId": {"type": "string", "description": ""}, "pages": {"type": "integer", "format": "int64"}}, "description": "数据"}}, "description": "分页设备列表"}}}}}}}, "/devices/wakeOnLan/{id}": {"post": {"summary": "网络唤醒设备", "description": "网络唤醒设备", "parameters": [{"name": "id", "in": "path", "description": "设备ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "唤醒结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "唤醒结果"}}}}}}}, "/policyTag/getAllByExhibition/{exhibitionId}": {"get": {"summary": "查询所属展厅下所有标签", "description": "查询所属展厅下所有标签", "parameters": [{"name": "exhibitionId", "in": "path", "description": "", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "array", "description": "数据", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键"}, "exhibitionId": {"type": "integer", "description": "展厅id"}, "tagName": {"type": "string", "description": "标签名称"}, "tagDescription": {"type": "string", "description": "标签描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "启用状态"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}}, "description": "设备策略标签表"}}}}}}}}}}, "/policyTag/add": {"post": {"summary": "添加标签", "description": "添加标签", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键"}, "exhibitionId": {"type": "integer", "description": "展厅id"}, "tagName": {"type": "string", "description": "标签名称"}, "tagDescription": {"type": "string", "description": "标签描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "启用状态"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}}, "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}}}}}}}}, "/policyTag/update/{tagId}": {"put": {"summary": "更新标签", "description": "更新标签", "parameters": [{"name": "tagId", "in": "path", "description": "", "required": true, "schema": {"type": "integer"}}, {"name": "name", "in": "query", "description": "", "required": true, "schema": {"type": "string", "nullable": false}}, {"name": "description", "in": "query", "description": "", "required": true, "schema": {"type": "string", "nullable": false}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}}}}}}}}, "/policyTag/removeBatch": {"delete": {"summary": "批量删除标签", "description": "批量删除标签", "parameters": [{"name": "Ids", "in": "query", "description": "", "required": true, "schema": {"type": "array", "nullable": false, "items": {"type": "integer"}}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "integer", "description": "数据"}}}}}}}}}, "/policyTag/page/{exhibitionId}": {"get": {"summary": "分页查询所属展厅下标签", "description": "分页查询所属展厅下标签", "parameters": [{"name": "exhibitionId", "in": "path", "description": "", "required": true, "schema": {"type": "integer"}}, {"name": "pageNum", "in": "query", "description": "", "required": true, "schema": {"type": "integer", "nullable": false, "default": "1"}}, {"name": "pageSize", "in": "query", "description": "", "required": true, "schema": {"type": "integer", "nullable": false, "default": "10"}}, {"name": "name", "in": "query", "description": "", "required": false, "schema": {"type": "string", "nullable": false}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"records": {"type": "array", "description": "", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键"}, "exhibitionId": {"type": "integer", "description": "展厅id"}, "tagName": {"type": "string", "description": "标签名称"}, "tagDescription": {"type": "string", "description": "标签描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "启用状态"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}}, "description": "设备策略标签表"}}, "total": {"type": "integer", "description": "", "format": "int64"}, "size": {"type": "integer", "description": "", "format": "int64"}, "current": {"type": "integer", "description": "", "format": "int64"}, "orders": {"type": "array", "description": "", "items": {"type": "object", "properties": {"column": {"type": "string", "description": ""}, "asc": {"type": "boolean", "description": ""}}, "description": "com.baomidou.mybatisplus.core.metadata.OrderItem"}}, "optimizeCountSql": {"type": "boolean", "description": ""}, "searchCount": {"type": "boolean", "description": ""}, "optimizeJoinOfCountSql": {"type": "boolean", "description": ""}, "maxLimit": {"type": "integer", "description": "", "format": "int64"}, "countId": {"type": "string", "description": ""}, "pages": {"type": "integer", "format": "int64"}}, "description": "数据"}}}}}}}}}, "/policyTag/updateStatus/{tagId}": {"put": {"summary": "修改标签使用状态", "description": "修改标签使用状态", "parameters": [{"name": "tagId", "in": "path", "description": "", "required": true, "schema": {"type": "integer"}}, {"name": "status", "in": "query", "description": "", "required": true, "schema": {"type": "integer", "nullable": false}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}}}}}}}}, "/deviceTypes/add": {"post": {"summary": "添加设备类型", "description": "添加设备类型", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "产品ID"}, "exhibitionId": {"type": "integer", "description": "所属展览馆ID"}, "name": {"type": "string", "description": "产品名称"}, "description": {"type": "string", "description": "产品描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "状态(1:发布,0:未发布)"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"maximum": 128, "minimum": -127, "type": "integer", "description": "逻辑删除"}}, "description": "设备类型信息"}}}}, "responses": {"200": {"description": "添加结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "添加结果"}}}}}}}, "/deviceTypes/update": {"post": {"summary": "更新设备类型", "description": "更新设备类型", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "产品ID"}, "exhibitionId": {"type": "integer", "description": "所属展览馆ID"}, "name": {"type": "string", "description": "产品名称"}, "description": {"type": "string", "description": "产品描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "状态(1:发布,0:未发布)"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"maximum": 128, "minimum": -127, "type": "integer", "description": "逻辑删除"}}, "description": "设备类型信息"}}}}, "responses": {"200": {"description": "更新结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "更新结果"}}}}}}}, "/deviceTypes/del/{id}": {"delete": {"summary": "删除设备类型", "description": "删除设备类型", "parameters": [{"name": "id", "in": "path", "description": "设备类型ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "删除结果"}}}}}}}, "/deviceTypes/{id}": {"get": {"summary": "获取设备类型详情", "description": "获取设备类型详情", "parameters": [{"name": "id", "in": "path", "description": "设备类型ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "设备类型详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "产品ID"}, "exhibitionId": {"type": "integer", "description": "所属展览馆ID"}, "name": {"type": "string", "description": "产品名称"}, "description": {"type": "string", "description": "产品描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "状态(1:发布,0:未发布)"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"maximum": 128, "minimum": -127, "type": "integer", "description": "逻辑删除"}}, "description": "数据"}}, "description": "设备类型详情"}}}}}}}, "/deviceTypes/page/{exhibitionId}": {"get": {"summary": "分页查询设备类型", "description": "分页查询设备类型", "parameters": [{"name": "exhibitionId", "in": "path", "description": "展厅ID（可选）", "required": true, "schema": {"type": "integer"}}, {"name": "pageNum", "in": "query", "description": "页码", "required": true, "schema": {"type": "integer", "nullable": false, "default": "1"}}, {"name": "pageSize", "in": "query", "description": "每页大小", "required": true, "schema": {"type": "integer", "nullable": false, "default": "10"}}, {"name": "name", "in": "query", "description": "设备类型名称（可选，用于模糊查询）", "required": false, "schema": {"type": "string", "nullable": false}}], "responses": {"200": {"description": "分页设备类型列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"records": {"type": "array", "description": "", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "产品ID"}, "exhibitionId": {"type": "integer", "description": "所属展览馆ID"}, "name": {"type": "string", "description": "产品名称"}, "description": {"type": "string", "description": "产品描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "状态(1:发布,0:未发布)"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"maximum": 128, "minimum": -127, "type": "integer", "description": "逻辑删除"}}, "description": "zb.iot.entity.DeviceType"}}, "total": {"type": "integer", "description": "", "format": "int64"}, "size": {"type": "integer", "description": "", "format": "int64"}, "current": {"type": "integer", "description": "", "format": "int64"}, "orders": {"type": "array", "description": "", "items": {"type": "object", "properties": {"column": {"type": "string", "description": ""}, "asc": {"type": "boolean", "description": ""}}, "description": "com.baomidou.mybatisplus.core.metadata.OrderItem"}}, "optimizeCountSql": {"type": "boolean", "description": ""}, "searchCount": {"type": "boolean", "description": ""}, "optimizeJoinOfCountSql": {"type": "boolean", "description": ""}, "maxLimit": {"type": "integer", "description": "", "format": "int64"}, "countId": {"type": "string", "description": ""}, "pages": {"type": "integer", "format": "int64"}}, "description": "数据"}}, "description": "分页设备类型列表"}}}}}}}, "/deviceTypes/publish/{id}": {"post": {"summary": "发布设备类型", "description": "发布设备类型", "parameters": [{"name": "id", "in": "path", "description": "设备类型ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "发布结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "发布结果"}}}}}}}, "/deviceTypes/unpublish/{id}": {"post": {"summary": "取消发布设备类型", "description": "取消发布设备类型", "parameters": [{"name": "id", "in": "path", "description": "设备类型ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "取消发布结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "取消发布结果"}}}}}}}, "/deviceTypes/published": {"get": {"summary": "获取所有已发布的设备类型", "description": "获取所有已发布的设备类型", "parameters": [{"name": "exhibitionId", "in": "query", "description": "展厅ID（可选）", "required": false, "schema": {"type": "integer", "nullable": false}}], "responses": {"200": {"description": "已发布的设备类型列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "array", "description": "数据", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "产品ID"}, "exhibitionId": {"type": "integer", "description": "所属展览馆ID"}, "name": {"type": "string", "description": "产品名称"}, "description": {"type": "string", "description": "产品描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "状态(1:发布,0:未发布)"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"maximum": 128, "minimum": -127, "type": "integer", "description": "逻辑删除"}}, "description": "zb.iot.entity.DeviceType"}}}, "description": "已发布的设备类型列表"}}}}}}}, "/exhibition/getExhibition": {"post": {"summary": "分页查询展厅列表", "description": "分页查询展厅列表", "parameters": [{"name": "page", "in": "query", "description": "当前页码，默认为1", "required": true, "schema": {"type": "integer", "nullable": false, "default": "1"}}, {"name": "pageSize", "in": "query", "description": "每页记录数，默认为8", "required": true, "schema": {"type": "integer", "nullable": false, "default": "8"}}, {"name": "exhibitionName", "in": "query", "description": "展厅名称，可选参数，用于模糊查询", "required": false, "schema": {"type": "string", "nullable": false}}], "responses": {"200": {"description": "分页结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"records": {"type": "array", "description": "", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": ""}, "name": {"type": "string", "description": ""}, "logoUrl": {"type": "string", "description": ""}, "description": {"type": "string", "description": ""}, "address": {"type": "string", "description": ""}, "exhibitionIP": {"type": "string", "description": ""}, "createTime": {"type": "string", "description": ""}}, "description": "zb.iot.controller.dto.ExhibitionDTO"}}, "total": {"type": "integer", "description": "", "format": "int64"}, "size": {"type": "integer", "description": "", "format": "int64"}, "current": {"type": "integer", "description": "", "format": "int64"}, "orders": {"type": "array", "description": "", "items": {"type": "object", "properties": {"column": {"type": "string", "description": ""}, "asc": {"type": "boolean", "description": ""}}, "description": "com.baomidou.mybatisplus.core.metadata.OrderItem"}}, "optimizeCountSql": {"type": "boolean", "description": ""}, "searchCount": {"type": "boolean", "description": ""}, "optimizeJoinOfCountSql": {"type": "boolean", "description": ""}, "maxLimit": {"type": "integer", "description": "", "format": "int64"}, "countId": {"type": "string", "description": ""}, "pages": {"type": "integer", "format": "int64"}}, "description": "数据"}}, "description": "分页结果"}}}}}}}, "/exhibition/addExhibition": {"post": {"summary": "添加展厅", "description": "添加展厅", "parameters": [{"name": "name", "in": "query", "description": "", "required": true, "schema": {"type": "string", "nullable": false}}, {"name": "description", "in": "query", "description": "", "required": true, "schema": {"type": "string", "nullable": false}}, {"name": "address", "in": "query", "description": "", "required": true, "schema": {"type": "string", "nullable": false}}, {"name": "exhibitionIP", "in": "query", "description": "", "required": true, "schema": {"type": "string", "nullable": false}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["logo"], "type": "object", "properties": {"logo": {"type": "string", "description": "图片", "format": "binary"}}}}}, "required": true}, "responses": {"200": {"description": "分页结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "分页结果"}}}}}}}, "/exhibition/updateExhibition": {"put": {"summary": "更新展厅信息", "description": "更新展厅信息", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "展厅ID"}, "name": {"type": "string", "description": "展厅名称"}, "address": {"type": "string", "description": "项目地址"}, "exhibitionIP": {"type": "string", "description": "项目IP"}, "description": {"type": "string", "description": "项目简介"}, "logoPath": {"type": "string", "description": "Logo存储路径"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "状态(1:启用,0:禁用)"}}, "description": "展厅信息对象"}}}}, "responses": {"200": {"description": "更新结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "更新结果"}}}}}}}, "/exhibition/removeExhibition/{id}": {"delete": {"summary": "根据ID删除展厅", "description": "根据ID删除展厅", "parameters": [{"name": "id", "in": "path", "description": "展厅ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "string", "description": "数据"}}, "description": "删除结果"}}}}}}}, "/administrator/add": {"post": {"summary": "添加管理员并分配权限", "description": "添加管理员并分配权限", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"exhibitionId": {"type": "integer", "description": "展厅ID"}, "userId": {"type": "integer", "description": "用户ID"}, "permissionIds": {"type": "array", "description": "权限ID列表", "items": {"type": "integer"}}}, "description": "包含展厅ID、用户ID和权限列表的请求"}}}}, "responses": {"200": {"description": "添加结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "管理员ID"}, "userId": {"type": "integer", "description": "用户ID"}, "exhibitionId": {"type": "integer", "description": "展厅ID"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}, "permissions": {"type": "array", "description": "管理员关联的权限列表 - 非数据库字段\n用于传输数据，不直接映射到数据库", "items": {"type": "integer"}}}, "description": "数据"}}, "description": "添加结果"}}}}}}}, "/administrator/{adminId}": {"get": {"summary": "获取管理员详情，包括权限信息", "description": "获取管理员详情，包括权限信息", "parameters": [{"name": "adminId", "in": "path", "description": "", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "管理员ID"}, "userId": {"type": "integer", "description": "用户ID"}, "exhibitionId": {"type": "integer", "description": "展厅ID"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}, "permissions": {"type": "array", "description": "管理员关联的权限列表 - 非数据库字段\n用于传输数据，不直接映射到数据库", "items": {"type": "integer"}}}, "description": "数据"}}}}}}}}}, "/administrator/{adminId}/permissions": {"put": {"summary": "更新管理员权限", "description": "更新管理员权限", "parameters": [{"name": "adminId", "in": "query", "description": "", "required": true, "schema": {"type": "integer", "nullable": false}}, {"name": "permissionIds", "in": "query", "description": "", "required": false, "schema": {"type": "array", "nullable": false, "items": {"type": "integer"}}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "管理员ID"}, "userId": {"type": "integer", "description": "用户ID"}, "exhibitionId": {"type": "integer", "description": "展厅ID"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}, "permissions": {"type": "array", "description": "管理员关联的权限列表 - 非数据库字段\n用于传输数据，不直接映射到数据库", "items": {"type": "integer"}}}, "description": "数据"}}}}}}}}}, "/administrator/exhibition/{exhibitionId}": {"get": {"summary": "根据展厅ID获取所有管理员", "description": "根据展厅ID获取所有管理员", "parameters": [{"name": "exhibitionId", "in": "path", "description": "", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "array", "description": "数据", "items": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户ID"}, "username": {"type": "string", "description": "用户名称"}, "avatarUrl": {"type": "string", "description": "头像URL"}, "registerTime": {"type": "string", "description": "注册时间"}, "status": {"type": "integer", "description": "用户状态 (1-正常, 0-禁用)"}}, "description": "管理员信息DTO"}}}}}}}}}}, "/administrator/non-admin-users/{exhibitionId}": {"get": {"summary": "根据展厅ID获取非管理员用户", "description": "根据展厅ID获取非管理员用户", "parameters": [{"name": "exhibitionId", "in": "path", "description": "", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "status": {"type": "boolean", "description": "状态信息"}, "message": {"type": "string", "description": "返回信息"}, "data": {"type": "array", "description": "数据", "items": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户ID"}, "username": {"type": "string", "description": "用户名称"}, "avatarUrl": {"type": "string", "description": "头像URL"}, "registerTime": {"type": "string", "description": "注册时间"}, "status": {"type": "integer", "description": "用户状态 (1-正常, 0-禁用)"}}, "description": "管理员信息DTO"}}}, "description": ""}}}}}}}}, "components": {"schemas": {"zb.iot.controller.dto.AuthResponse": {"type": "object", "properties": {"token": {"type": "string", "description": ""}, "userId": {"type": "integer", "description": ""}, "permissions": {"type": "array", "description": "", "items": {"type": "integer"}}}, "description": "数据"}, "设备策略标签表": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键"}, "exhibitionId": {"type": "integer", "description": "展厅id"}, "tagName": {"type": "string", "description": "标签名称"}, "tagDescription": {"type": "string", "description": "标签描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "启用状态"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}}, "description": "设备策略标签表"}, "物联网设备表": {"type": "object", "properties": {"id": {"type": "integer", "description": "设备ID"}, "exhibitionId": {"type": "integer", "description": "所属展厅ID"}, "name": {"type": "string", "description": "设备名称"}, "ipAddress": {"type": "string", "description": "IP地址"}, "macAddress": {"type": "string", "description": "MAC地址"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "设备状态(0:离线,1:在线)"}, "identifier": {"type": "string", "description": "设备标识"}, "deviceType": {"type": "integer", "description": "设备类型"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}, "policyTag": {"type": "array", "description": "设备关联的权限列表 - 非数据库字段\n用于传输数据，不直接映射到数据库", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键"}, "exhibitionId": {"type": "integer", "description": "展厅id"}, "tagName": {"type": "string", "description": "标签名称"}, "tagDescription": {"type": "string", "description": "标签描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "启用状态"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}}, "description": "设备策略标签表"}}}, "description": "物联网设备表"}, "com.baomidou.mybatisplus.core.metadata.OrderItem": {"type": "object", "properties": {"column": {"type": "string", "description": ""}, "asc": {"type": "boolean", "description": ""}}, "description": "com.baomidou.mybatisplus.core.metadata.OrderItem"}, "zb.iot.entity.DeviceType": {"type": "object", "properties": {"id": {"type": "integer", "description": "产品ID"}, "exhibitionId": {"type": "integer", "description": "所属展览馆ID"}, "name": {"type": "string", "description": "产品名称"}, "description": {"type": "string", "description": "产品描述"}, "status": {"maximum": 128, "minimum": -127, "type": "integer", "description": "状态(1:发布,0:未发布)"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"maximum": 128, "minimum": -127, "type": "integer", "description": "逻辑删除"}}, "description": "zb.iot.entity.DeviceType"}, "zb.iot.controller.dto.ExhibitionDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": ""}, "name": {"type": "string", "description": ""}, "logoUrl": {"type": "string", "description": ""}, "description": {"type": "string", "description": ""}, "address": {"type": "string", "description": ""}, "exhibitionIP": {"type": "string", "description": ""}, "createTime": {"type": "string", "description": ""}}, "description": "zb.iot.controller.dto.ExhibitionDTO"}, "展厅管理员表": {"type": "object", "properties": {"id": {"type": "integer", "description": "管理员ID"}, "userId": {"type": "integer", "description": "用户ID"}, "exhibitionId": {"type": "integer", "description": "展厅ID"}, "createTime": {"type": "string", "description": "创建时间"}, "updateTime": {"type": "string", "description": "更新时间"}, "deleted": {"type": "integer", "description": "逻辑删除"}, "permissions": {"type": "array", "description": "管理员关联的权限列表 - 非数据库字段\n用于传输数据，不直接映射到数据库", "items": {"type": "integer"}}}, "description": "数据"}, "管理员信息DTO": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户ID"}, "username": {"type": "string", "description": "用户名称"}, "avatarUrl": {"type": "string", "description": "头像URL"}, "registerTime": {"type": "string", "description": "注册时间"}, "status": {"type": "integer", "description": "用户状态 (1-正常, 0-禁用)"}}, "description": "管理员信息DTO"}}}}