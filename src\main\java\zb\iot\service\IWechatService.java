package zb.iot.service;

import zb.iot.entity.User;
import zb.iot.entity.WechatUser;

import java.util.Map;

/**
 * 微信服务接口
 */
public interface IWechatService {
    
    /**
     * 通过授权码获取微信用户信息
     * @param code 微信授权码
     * @return 微信用户信息
     */
    Map<String, Object> getWechatUserInfo(String code);
    
    /**
     * 根据OpenID查找或创建用户
     * @param openId 微信OpenID
     * @param unionId 微信UnionID
     * @param wechatInfo 微信用户信息
     * @return 用户对象
     */
    User getUserByWechatOpenId(String openId, String unionId, Map<String, Object> wechatInfo);
    
    /**
     * 关联微信用户与系统用户
     * @param userId 用户ID
     * @param openId 微信OpenID
     * @param unionId 微信UnionID
     * @param wechatInfo 微信用户信息
     * @return 微信用户关联对象
     */
    WechatUser linkWechatUser(Integer userId, String openId, String unionId, Map<String, Object> wechatInfo);
} 