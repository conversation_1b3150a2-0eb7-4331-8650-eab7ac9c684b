package zb.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import zb.iot.controller.dto.StrategyDTO;
import zb.iot.controller.dto.StrategyQueryDTO;
import zb.iot.entity.Strategy;
import zb.iot.entity.StrategyExecutionLog;
import zb.iot.entity.VO.StrategyVO;

import java.util.List;

/**
 * 策略管理服务接口
 * <AUTHOR>
 * @since 2025-01-31
 */
public interface IStrategyService extends IService<Strategy> {

    /**
     * 分页查询策略列表
     * @param queryDTO 查询条件
     * @return 策略分页数据
     */
    IPage<StrategyVO> getStrategyPage(StrategyQueryDTO queryDTO);

    /**
     * 根据ID查询策略详情
     * @param id 策略ID
     * @return 策略详情
     */
    StrategyVO getStrategyById(Integer id);

    /**
     * 创建策略
     * @param strategyDTO 策略数据
     * @return 创建的策略
     */
    Strategy createStrategy(StrategyDTO strategyDTO);

    /**
     * 更新策略
     * @param strategyDTO 策略数据
     * @return 更新的策略
     */
    Strategy updateStrategy(StrategyDTO strategyDTO);

    /**
     * 删除策略
     * @param id 策略ID
     * @return 是否删除成功
     */
    boolean deleteStrategy(Integer id);

    /**
     * 启用/禁用策略
     * @param id 策略ID
     * @param status 状态(0:禁用,1:启用)
     * @return 是否操作成功
     */
    boolean toggleStrategyStatus(Integer id, Integer status);

    /**
     * 执行策略
     * @param id 策略ID
     * @return 执行结果
     */
    StrategyExecutionLog executeStrategy(Integer id);

    /**
     * 手动执行策略
     * @param id 策略ID
     * @param triggerType 触发类型
     * @return 执行结果
     */
    StrategyExecutionLog executeStrategy(Integer id, String triggerType);

    /**
     * 验证策略配置
     * @param type 策略类型
     * @param config 策略配置JSON
     * @return 验证结果
     */
    boolean validateStrategyConfig(Integer type, String config);

    /**
     * 获取策略执行历史
     * @param strategyId 策略ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 执行历史分页数据
     */
    IPage<StrategyExecutionLog> getExecutionHistory(Integer strategyId, Integer pageNum, Integer pageSize);

    /**
     * 查询指定展厅的启用策略
     * @param exhibitionId 展厅ID
     * @return 策略列表
     */
    List<Strategy> getEnabledStrategies(Integer exhibitionId);

    /**
     * 查询指定类型的启用策略
     * @param exhibitionId 展厅ID
     * @param type 策略类型
     * @return 策略列表
     */
    List<Strategy> getEnabledStrategiesByType(Integer exhibitionId, Integer type);

    /**
     * 批量执行策略
     * @param strategyIds 策略ID列表
     * @return 执行结果列表
     */
    List<StrategyExecutionLog> batchExecuteStrategies(List<Integer> strategyIds);
}
